{"ast": null, "code": "import { isControllingVariants } from '../../render/utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from '../../render/utils/is-variant-label.mjs';\nfunction getCurrentTreeVariants(props, context) {\n  if (isControllingVariants(props)) {\n    const {\n      initial,\n      animate\n    } = props;\n    return {\n      initial: initial === false || isVariantLabel(initial) ? initial : undefined,\n      animate: isVariantLabel(animate) ? animate : undefined\n    };\n  }\n  return props.inherit !== false ? context : {};\n}\nexport { getCurrentTreeVariants };", "map": {"version": 3, "names": ["isControllingVariants", "isVariantLabel", "getCurrentTreeVariants", "props", "context", "initial", "animate", "undefined", "inherit"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs"], "sourcesContent": ["import { isControllingVariants } from '../../render/utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from '../../render/utils/is-variant-label.mjs';\n\nfunction getCurrentTreeVariants(props, context) {\n    if (isControllingVariants(props)) {\n        const { initial, animate } = props;\n        return {\n            initial: initial === false || isVariantLabel(initial)\n                ? initial\n                : undefined,\n            animate: isVariantLabel(animate) ? animate : undefined,\n        };\n    }\n    return props.inherit !== false ? context : {};\n}\n\nexport { getCurrentTreeVariants };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,gDAAgD;AACtF,SAASC,cAAc,QAAQ,yCAAyC;AAExE,SAASC,sBAAsBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5C,IAAIJ,qBAAqB,CAACG,KAAK,CAAC,EAAE;IAC9B,MAAM;MAAEE,OAAO;MAAEC;IAAQ,CAAC,GAAGH,KAAK;IAClC,OAAO;MACHE,OAAO,EAAEA,OAAO,KAAK,KAAK,IAAIJ,cAAc,CAACI,OAAO,CAAC,GAC/CA,OAAO,GACPE,SAAS;MACfD,OAAO,EAAEL,cAAc,CAACK,OAAO,CAAC,GAAGA,OAAO,GAAGC;IACjD,CAAC;EACL;EACA,OAAOJ,KAAK,CAACK,OAAO,KAAK,KAAK,GAAGJ,OAAO,GAAG,CAAC,CAAC;AACjD;AAEA,SAASF,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}