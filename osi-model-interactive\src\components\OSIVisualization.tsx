import React, { useState } from 'react';
import { AnimatePresence } from 'framer-motion';
import OSILayer from './OSILayer';
import DetailPanel from './DetailPanel';
import DataFlowAnimation from './DataFlowAnimation';
import { OSI_LAYERS } from '../types/OSIModel';

const OSIVisualization: React.FC = () => {
  const [selectedLayer, setSelectedLayer] = useState<number | null>(null);
  const [hoveredLayer, setHoveredLayer] = useState<number | null>(null);
  const [showDataFlow, setShowDataFlow] = useState(false);
  const [dataFlowDirection, setDataFlowDirection] = useState<'down' | 'up'>('down');

  const handleLayerClick = (layerId: number) => {
    setSelectedLayer(selectedLayer === layerId ? null : layerId);
  };

  const handleLayerHover = (layerId: number, isHovered: boolean) => {
    setHoveredLayer(isHovered ? layerId : null);
  };

  const startDataFlow = (direction: 'down' | 'up') => {
    setDataFlowDirection(direction);
    setShowDataFlow(true);
  };

  const stopDataFlow = () => {
    setShowDataFlow(false);
  };

  return (
    <div className="osi-visualization">
      <div className="visualization-header">
        <h1>OSI 七层网络模型</h1>
        <p>开放式系统互联通信参考模型 (Open System Interconnection Reference Model)</p>
        
        <div className="control-panel">
          <button 
            className="flow-button send"
            onClick={() => startDataFlow('down')}
            disabled={showDataFlow}
          >
            📤 发送数据
          </button>
          <button 
            className="flow-button receive"
            onClick={() => startDataFlow('up')}
            disabled={showDataFlow}
          >
            📥 接收数据
          </button>
          <button 
            className="flow-button stop"
            onClick={stopDataFlow}
            disabled={!showDataFlow}
          >
            ⏹️ 停止演示
          </button>
        </div>
      </div>

      <div className="visualization-content">
        <div className="layers-container">
          <div className="layers-stack">
            {OSI_LAYERS.map((layer) => (
              <OSILayer
                key={layer.id}
                layer={layer}
                isSelected={selectedLayer === layer.id}
                isHovered={hoveredLayer === layer.id}
                onClick={() => handleLayerClick(layer.id)}
                onHover={(isHovered) => handleLayerHover(layer.id, isHovered)}
              />
            ))}
          </div>

          <AnimatePresence>
            {showDataFlow && (
              <DataFlowAnimation
                direction={dataFlowDirection}
                onComplete={stopDataFlow}
              />
            )}
          </AnimatePresence>
        </div>

        <AnimatePresence>
          {selectedLayer && (
            <DetailPanel
              layer={OSI_LAYERS.find(l => l.id === selectedLayer)!}
              onClose={() => setSelectedLayer(null)}
            />
          )}
        </AnimatePresence>
      </div>

      <div className="visualization-footer">
        <div className="legend">
          <h3>图例说明</h3>
          <div className="legend-items">
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#FF6B6B' }}></div>
              <span>应用层 - 用户接口</span>
            </div>
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#4ECDC4' }}></div>
              <span>表示层 - 数据格式</span>
            </div>
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#45B7D1' }}></div>
              <span>会话层 - 连接管理</span>
            </div>
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#96CEB4' }}></div>
              <span>传输层 - 端到端传输</span>
            </div>
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#FFEAA7' }}></div>
              <span>网络层 - 路由选择</span>
            </div>
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#DDA0DD' }}></div>
              <span>数据链路层 - 帧传输</span>
            </div>
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#FFB6C1' }}></div>
              <span>物理层 - 比特传输</span>
            </div>
          </div>
        </div>

        <div className="instructions">
          <h3>使用说明</h3>
          <ul>
            <li>点击任意层查看详细信息</li>
            <li>使用控制按钮观看数据流动演示</li>
            <li>鼠标悬停查看层的基本信息</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default OSIVisualization;
