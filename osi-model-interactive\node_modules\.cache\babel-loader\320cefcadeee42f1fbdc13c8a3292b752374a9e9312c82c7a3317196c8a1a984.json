{"ast": null, "code": "const isNotNull = value => value !== null;\nfunction getFinalKeyframe(keyframes, {\n  repeat,\n  repeatType = \"loop\"\n}, finalKeyframe) {\n  const resolvedKeyframes = keyframes.filter(isNotNull);\n  const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1 ? 0 : resolvedKeyframes.length - 1;\n  return !index || finalKeyframe === undefined ? resolvedKeyframes[index] : finalKeyframe;\n}\nexport { getFinalKeyframe };", "map": {"version": 3, "names": ["isNotNull", "value", "getFinalKeyframe", "keyframes", "repeat", "repeatType", "finalKeyframe", "resolvedKeyframes", "filter", "index", "length", "undefined"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs"], "sourcesContent": ["const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAIC,KAAK,IAAKA,KAAK,KAAK,IAAI;AAC3C,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EAAEC,MAAM;EAAEC,UAAU,GAAG;AAAO,CAAC,EAAEC,aAAa,EAAE;EACjF,MAAMC,iBAAiB,GAAGJ,SAAS,CAACK,MAAM,CAACR,SAAS,CAAC;EACrD,MAAMS,KAAK,GAAGL,MAAM,IAAIC,UAAU,KAAK,MAAM,IAAID,MAAM,GAAG,CAAC,KAAK,CAAC,GAC3D,CAAC,GACDG,iBAAiB,CAACG,MAAM,GAAG,CAAC;EAClC,OAAO,CAACD,KAAK,IAAIH,aAAa,KAAKK,SAAS,GACtCJ,iBAAiB,CAACE,KAAK,CAAC,GACxBH,aAAa;AACvB;AAEA,SAASJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}