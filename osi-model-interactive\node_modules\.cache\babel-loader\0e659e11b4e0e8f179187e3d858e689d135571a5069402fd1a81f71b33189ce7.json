{"ast": null, "code": "import { backIn } from './back.mjs';\nconst anticipate = p => (p *= 2) < 1 ? 0.5 * backIn(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\nexport { anticipate };", "map": {"version": 3, "names": ["backIn", "anticipate", "p", "Math", "pow"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-utils/dist/es/easing/anticipate.mjs"], "sourcesContent": ["import { backIn } from './back.mjs';\n\nconst anticipate = (p) => (p *= 2) < 1 ? 0.5 * backIn(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n\nexport { anticipate };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,YAAY;AAEnC,MAAMC,UAAU,GAAIC,CAAC,IAAK,CAACA,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAGF,MAAM,CAACE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAEjG,SAASD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}