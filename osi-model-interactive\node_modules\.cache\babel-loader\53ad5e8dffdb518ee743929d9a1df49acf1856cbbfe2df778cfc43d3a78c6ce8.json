{"ast": null, "code": "import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nconst useHTMLVisualState = /*@__PURE__*/makeUseVisualState({\n  scrapeMotionValuesFromProps,\n  createRenderState: createHtmlRenderState\n});\nexport { useHTMLVisualState };", "map": {"version": 3, "names": ["makeUseVisualState", "createHtmlRenderState", "scrapeMotionValuesFromProps", "useHTMLVisualState", "createRenderState"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/render/html/use-html-visual-state.mjs"], "sourcesContent": ["import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nconst useHTMLVisualState = /*@__PURE__*/ makeUseVisualState({\n    scrapeMotionValuesFromProps,\n    createRenderState: createHtmlRenderState,\n});\n\nexport { useHTMLVisualState };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9E,MAAMC,kBAAkB,GAAG,aAAcH,kBAAkB,CAAC;EACxDE,2BAA2B;EAC3BE,iBAAiB,EAAEH;AACvB,CAAC,CAAC;AAEF,SAASE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}