{"ast": null, "code": "import { Children, isValidElement } from 'react';\nconst getChildKey = child => child.key || \"\";\nfunction onlyElements(children) {\n  const filtered = [];\n  // We use forEach here instead of map as map mutates the component key by preprending `.$`\n  Children.forEach(children, child => {\n    if (isValidElement(child)) filtered.push(child);\n  });\n  return filtered;\n}\nexport { getChildKey, onlyElements };", "map": {"version": 3, "names": ["Children", "isValidElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "key", "onlyElements", "children", "filtered", "for<PERSON>ach", "push"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs"], "sourcesContent": ["import { Children, isValidElement } from 'react';\n\nconst getChildKey = (child) => child.key || \"\";\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    Children.forEach(children, (child) => {\n        if (isValidElement(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n\nexport { getChildKey, onlyElements };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,cAAc,QAAQ,OAAO;AAEhD,MAAMC,WAAW,GAAIC,KAAK,IAAKA,KAAK,CAACC,GAAG,IAAI,EAAE;AAC9C,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC5B,MAAMC,QAAQ,GAAG,EAAE;EACnB;EACAP,QAAQ,CAACQ,OAAO,CAACF,QAAQ,EAAGH,KAAK,IAAK;IAClC,IAAIF,cAAc,CAACE,KAAK,CAAC,EACrBI,QAAQ,CAACE,IAAI,CAACN,KAAK,CAAC;EAC5B,CAAC,CAAC;EACF,OAAOI,QAAQ;AACnB;AAEA,SAASL,WAAW,EAAEG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}