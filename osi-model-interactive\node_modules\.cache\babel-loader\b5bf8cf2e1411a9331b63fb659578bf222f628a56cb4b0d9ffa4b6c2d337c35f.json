{"ast": null, "code": "\"use client\";\n\nimport { jsx } from 'react/jsx-runtime';\nimport { isMotionValue } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/components/motion/proxy.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\nfunction useDefaultMotionValue(value, defaultValue = 0) {\n  return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItemComponent({\n  children,\n  style = {},\n  value,\n  as = \"li\",\n  onDrag,\n  layout = true,\n  ...props\n}, externalRef) {\n  const Component = useConstant(() => motion[as]);\n  const context = useContext(ReorderContext);\n  const point = {\n    x: useDefaultMotionValue(style.x),\n    y: useDefaultMotionValue(style.y)\n  };\n  const zIndex = useTransform([point.x, point.y], ([latestX, latestY]) => latestX || latestY ? 1 : \"unset\");\n  invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\", \"reorder-item-child\");\n  const {\n    axis,\n    registerItem,\n    updateOrder\n  } = context;\n  return jsx(Component, {\n    drag: axis,\n    ...props,\n    dragSnapToOrigin: true,\n    style: {\n      ...style,\n      x: point.x,\n      y: point.y,\n      zIndex\n    },\n    layout: layout,\n    onDrag: (event, gesturePoint) => {\n      const {\n        velocity\n      } = gesturePoint;\n      velocity[axis] && updateOrder(value, point[axis].get(), velocity[axis]);\n      onDrag && onDrag(event, gesturePoint);\n    },\n    onLayoutMeasure: measured => registerItem(value, measured),\n    ref: externalRef,\n    ignoreStrict: true,\n    children: children\n  });\n}\nconst ReorderItem = /*@__PURE__*/forwardRef(ReorderItemComponent);\nexport { ReorderItem, ReorderItemComponent };", "map": {"version": 3, "names": ["jsx", "isMotionValue", "invariant", "forwardRef", "useContext", "ReorderContext", "motion", "useConstant", "useMotionValue", "useTransform", "useDefaultMotionValue", "value", "defaultValue", "ReorderItemComponent", "children", "style", "as", "onDrag", "layout", "props", "externalRef", "Component", "context", "point", "x", "y", "zIndex", "latestX", "latestY", "Boolean", "axis", "registerItem", "updateOrder", "drag", "dragSnapToO<PERSON>in", "event", "gesturePoint", "velocity", "get", "onLayoutMeasure", "measured", "ref", "ignoreStrict", "ReorderItem"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/components/Reorder/Item.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { isMotionValue } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/components/motion/proxy.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\n\nfunction useDefaultMotionValue(value, defaultValue = 0) {\n    return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItemComponent({ children, style = {}, value, as = \"li\", onDrag, layout = true, ...props }, externalRef) {\n    const Component = useConstant(() => motion[as]);\n    const context = useContext(ReorderContext);\n    const point = {\n        x: useDefaultMotionValue(style.x),\n        y: useDefaultMotionValue(style.y),\n    };\n    const zIndex = useTransform([point.x, point.y], ([latestX, latestY]) => latestX || latestY ? 1 : \"unset\");\n    invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\", \"reorder-item-child\");\n    const { axis, registerItem, updateOrder } = context;\n    return (jsx(Component, { drag: axis, ...props, dragSnapToOrigin: true, style: { ...style, x: point.x, y: point.y, zIndex }, layout: layout, onDrag: (event, gesturePoint) => {\n            const { velocity } = gesturePoint;\n            velocity[axis] &&\n                updateOrder(value, point[axis].get(), velocity[axis]);\n            onDrag && onDrag(event, gesturePoint);\n        }, onLayoutMeasure: (measured) => registerItem(value, measured), ref: externalRef, ignoreStrict: true, children: children }));\n}\nconst ReorderItem = /*@__PURE__*/ forwardRef(ReorderItemComponent);\n\nexport { ReorderItem, ReorderItemComponent };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,0CAA0C;AACjE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,+BAA+B;AAE5D,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,YAAY,GAAG,CAAC,EAAE;EACpD,OAAOX,aAAa,CAACU,KAAK,CAAC,GAAGA,KAAK,GAAGH,cAAc,CAACI,YAAY,CAAC;AACtE;AACA,SAASC,oBAAoBA,CAAC;EAAEC,QAAQ;EAAEC,KAAK,GAAG,CAAC,CAAC;EAAEJ,KAAK;EAAEK,EAAE,GAAG,IAAI;EAAEC,MAAM;EAAEC,MAAM,GAAG,IAAI;EAAE,GAAGC;AAAM,CAAC,EAAEC,WAAW,EAAE;EACpH,MAAMC,SAAS,GAAGd,WAAW,CAAC,MAAMD,MAAM,CAACU,EAAE,CAAC,CAAC;EAC/C,MAAMM,OAAO,GAAGlB,UAAU,CAACC,cAAc,CAAC;EAC1C,MAAMkB,KAAK,GAAG;IACVC,CAAC,EAAEd,qBAAqB,CAACK,KAAK,CAACS,CAAC,CAAC;IACjCC,CAAC,EAAEf,qBAAqB,CAACK,KAAK,CAACU,CAAC;EACpC,CAAC;EACD,MAAMC,MAAM,GAAGjB,YAAY,CAAC,CAACc,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,CAAC,EAAE,CAAC,CAACE,OAAO,EAAEC,OAAO,CAAC,KAAKD,OAAO,IAAIC,OAAO,GAAG,CAAC,GAAG,OAAO,CAAC;EACzG1B,SAAS,CAAC2B,OAAO,CAACP,OAAO,CAAC,EAAE,+CAA+C,EAAE,oBAAoB,CAAC;EAClG,MAAM;IAAEQ,IAAI;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGV,OAAO;EACnD,OAAQtB,GAAG,CAACqB,SAAS,EAAE;IAAEY,IAAI,EAAEH,IAAI;IAAE,GAAGX,KAAK;IAAEe,gBAAgB,EAAE,IAAI;IAAEnB,KAAK,EAAE;MAAE,GAAGA,KAAK;MAAES,CAAC,EAAED,KAAK,CAACC,CAAC;MAAEC,CAAC,EAAEF,KAAK,CAACE,CAAC;MAAEC;IAAO,CAAC;IAAER,MAAM,EAAEA,MAAM;IAAED,MAAM,EAAEA,CAACkB,KAAK,EAAEC,YAAY,KAAK;MACrK,MAAM;QAAEC;MAAS,CAAC,GAAGD,YAAY;MACjCC,QAAQ,CAACP,IAAI,CAAC,IACVE,WAAW,CAACrB,KAAK,EAAEY,KAAK,CAACO,IAAI,CAAC,CAACQ,GAAG,CAAC,CAAC,EAAED,QAAQ,CAACP,IAAI,CAAC,CAAC;MACzDb,MAAM,IAAIA,MAAM,CAACkB,KAAK,EAAEC,YAAY,CAAC;IACzC,CAAC;IAAEG,eAAe,EAAGC,QAAQ,IAAKT,YAAY,CAACpB,KAAK,EAAE6B,QAAQ,CAAC;IAAEC,GAAG,EAAErB,WAAW;IAAEsB,YAAY,EAAE,IAAI;IAAE5B,QAAQ,EAAEA;EAAS,CAAC,CAAC;AACpI;AACA,MAAM6B,WAAW,GAAG,aAAcxC,UAAU,CAACU,oBAAoB,CAAC;AAElE,SAAS8B,WAAW,EAAE9B,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}