{"ast": null, "code": "import { progress } from 'motion-utils';\nimport { mixNumber } from '../../../utils/mix/number.mjs';\nfunction fillOffset(offset, remaining) {\n  const min = offset[offset.length - 1];\n  for (let i = 1; i <= remaining; i++) {\n    const offsetProgress = progress(0, remaining, i);\n    offset.push(mixNumber(min, 1, offsetProgress));\n  }\n}\nexport { fillOffset };", "map": {"version": 3, "names": ["progress", "mixNumber", "fillOffset", "offset", "remaining", "min", "length", "i", "offsetProgress", "push"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/animation/keyframes/offsets/fill.mjs"], "sourcesContent": ["import { progress } from 'motion-utils';\nimport { mixNumber } from '../../../utils/mix/number.mjs';\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = progress(0, remaining, i);\n        offset.push(mixNumber(min, 1, offsetProgress));\n    }\n}\n\nexport { fillOffset };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,SAAS,QAAQ,+BAA+B;AAEzD,SAASC,UAAUA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACnC,MAAMC,GAAG,GAAGF,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,SAAS,EAAEG,CAAC,EAAE,EAAE;IACjC,MAAMC,cAAc,GAAGR,QAAQ,CAAC,CAAC,EAAEI,SAAS,EAAEG,CAAC,CAAC;IAChDJ,MAAM,CAACM,IAAI,CAACR,SAAS,CAACI,GAAG,EAAE,CAAC,EAAEG,cAAc,CAAC,CAAC;EAClD;AACJ;AAEA,SAASN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}