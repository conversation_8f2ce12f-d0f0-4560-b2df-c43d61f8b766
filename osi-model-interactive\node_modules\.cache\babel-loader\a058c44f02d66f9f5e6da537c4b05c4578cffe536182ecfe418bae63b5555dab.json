{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\osi-model-interactive\\\\src\\\\components\\\\DataFlowAnimation.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { OSI_LAYERS } from '../types/OSIModel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataFlowAnimation = ({\n  direction,\n  onComplete\n}) => {\n  _s();\n  var _OSI_LAYERS$find;\n  const [currentPacket, setCurrentPacket] = useState(null);\n  const [animationStep, setAnimationStep] = useState(0);\n  const [isComplete, setIsComplete] = useState(false);\n  const initialData = direction === 'down' ? {\n    content: \"Hello World!\",\n    headers: []\n  } : {\n    content: \"HTTP Response\",\n    headers: [\"T<PERSON> Header\", \"IP Header\", \"Ethernet Header\"]\n  };\n  useEffect(() => {\n    if (isComplete) return;\n    const layers = direction === 'down' ? OSI_LAYERS : [...OSI_LAYERS].reverse();\n    const totalSteps = layers.length;\n    if (animationStep < totalSteps) {\n      const currentLayer = layers[animationStep];\n      const timer = setTimeout(() => {\n        setCurrentPacket({\n          id: `packet-${animationStep}`,\n          content: initialData.content,\n          currentLayer: currentLayer.id,\n          headers: direction === 'down' ? [...initialData.headers, `${currentLayer.name} Header`] : initialData.headers.slice(0, -animationStep)\n        });\n        setAnimationStep(prev => prev + 1);\n      }, 800);\n      return () => clearTimeout(timer);\n    } else {\n      const timer = setTimeout(() => {\n        setIsComplete(true);\n        onComplete();\n      }, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [animationStep, direction, onComplete, isComplete, initialData.content, initialData.headers]);\n  const getPacketPosition = layerId => {\n    const layer = OSI_LAYERS.find(l => l.id === layerId);\n    return layer ? layer.position.y + 40 : 0;\n  };\n  const getHeadersDisplay = () => {\n    if (!currentPacket) return [];\n    if (direction === 'down') {\n      // 发送时，逐层添加头部\n      return currentPacket.headers;\n    } else {\n      // 接收时，逐层移除头部\n      return currentPacket.headers;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"data-flow-animation\",\n    children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: currentPacket && !isComplete && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"data-packet\",\n        initial: {\n          x: direction === 'down' ? -100 : 600,\n          y: getPacketPosition(currentPacket.currentLayer),\n          scale: 0.8,\n          opacity: 0\n        },\n        animate: {\n          x: 500,\n          y: getPacketPosition(currentPacket.currentLayer),\n          scale: 1,\n          opacity: 1\n        },\n        exit: {\n          x: direction === 'down' ? 600 : -100,\n          opacity: 0,\n          scale: 0.8\n        },\n        transition: {\n          duration: 0.6,\n          ease: \"easeInOut\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"packet-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"packet-data\",\n            children: currentPacket.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"packet-headers\",\n            children: getHeadersDisplay().map((header, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"packet-header\",\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: index * 0.1\n              },\n              children: header\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"packet-arrow\",\n          children: direction === 'down' ? '⬇️' : '⬆️'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flow-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: direction === 'down' ? '📤 数据发送过程' : '📥 数据接收过程'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: direction === 'down' ? '数据从应用层向下传递，每层添加自己的头部信息' : '数据从物理层向上传递，每层移除对应的头部信息'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), currentPacket && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-layer-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"\\u5F53\\u524D\\u5904\\u7406\\u5C42:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this), (_OSI_LAYERS$find = OSI_LAYERS.find(l => l.id === currentPacket.currentLayer)) === null || _OSI_LAYERS$find === void 0 ? void 0 : _OSI_LAYERS$find.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"process-steps\",\n      children: OSI_LAYERS.map((layer, index) => {\n        const stepIndex = direction === 'down' ? 7 - layer.id : layer.id - 1;\n        const isActive = animationStep > stepIndex;\n        const isCurrent = animationStep === stepIndex + 1;\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `process-step ${isActive ? 'completed' : ''} ${isCurrent ? 'current' : ''}`,\n          initial: {\n            opacity: 0.3\n          },\n          animate: {\n            opacity: isActive ? 1 : isCurrent ? 0.8 : 0.3,\n            scale: isCurrent ? 1.1 : 1\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-number\",\n            children: layer.id\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-name\",\n            children: layer.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), isActive && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"step-checkmark\",\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              duration: 0.2\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 17\n          }, this)]\n        }, layer.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(DataFlowAnimation, \"DB2pvtA9KtgCEeMyMIjC7CyzKMY=\");\n_c = DataFlowAnimation;\nexport default DataFlowAnimation;\nvar _c;\n$RefreshReg$(_c, \"DataFlowAnimation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "OSI_LAYERS", "jsxDEV", "_jsxDEV", "DataFlowAnimation", "direction", "onComplete", "_s", "_OSI_LAYERS$find", "currentPacket", "setCurrentPacket", "animationStep", "setAnimationStep", "isComplete", "setIsComplete", "initialData", "content", "headers", "layers", "reverse", "totalSteps", "length", "<PERSON><PERSON><PERSON><PERSON>", "timer", "setTimeout", "id", "name", "slice", "prev", "clearTimeout", "getPacketPosition", "layerId", "layer", "find", "l", "position", "y", "getHeadersDisplay", "className", "children", "div", "initial", "x", "scale", "opacity", "animate", "exit", "transition", "duration", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "header", "index", "delay", "stepIndex", "isActive", "isCurrent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/src/components/DataFlowAnimation.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { OSI_LAYERS } from '../types/OSIModel';\n\ninterface DataFlowAnimationProps {\n  direction: 'down' | 'up';\n  onComplete: () => void;\n}\n\ninterface DataPacket {\n  id: string;\n  content: string;\n  currentLayer: number;\n  headers: string[];\n}\n\nconst DataFlowAnimation: React.FC<DataFlowAnimationProps> = ({ direction, onComplete }) => {\n  const [currentPacket, setCurrentPacket] = useState<DataPacket | null>(null);\n  const [animationStep, setAnimationStep] = useState(0);\n  const [isComplete, setIsComplete] = useState(false);\n\n  const initialData = direction === 'down' \n    ? { content: \"Hello World!\", headers: [] }\n    : { content: \"HTTP Response\", headers: [\"TCP Header\", \"IP Header\", \"Ethernet Header\"] };\n\n  useEffect(() => {\n    if (isComplete) return;\n\n    const layers = direction === 'down' ? OSI_LAYERS : [...OSI_LAYERS].reverse();\n    const totalSteps = layers.length;\n\n    if (animationStep < totalSteps) {\n      const currentLayer = layers[animationStep];\n      const timer = setTimeout(() => {\n        setCurrentPacket({\n          id: `packet-${animationStep}`,\n          content: initialData.content,\n          currentLayer: currentLayer.id,\n          headers: direction === 'down'\n            ? [...initialData.headers, `${currentLayer.name} Header`]\n            : initialData.headers.slice(0, -animationStep)\n        });\n        setAnimationStep(prev => prev + 1);\n      }, 800);\n\n      return () => clearTimeout(timer);\n    } else {\n      const timer = setTimeout(() => {\n        setIsComplete(true);\n        onComplete();\n      }, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [animationStep, direction, onComplete, isComplete, initialData.content, initialData.headers]);\n\n  const getPacketPosition = (layerId: number) => {\n    const layer = OSI_LAYERS.find(l => l.id === layerId);\n    return layer ? layer.position.y + 40 : 0;\n  };\n\n  const getHeadersDisplay = () => {\n    if (!currentPacket) return [];\n    \n    if (direction === 'down') {\n      // 发送时，逐层添加头部\n      return currentPacket.headers;\n    } else {\n      // 接收时，逐层移除头部\n      return currentPacket.headers;\n    }\n  };\n\n  return (\n    <div className=\"data-flow-animation\">\n      <AnimatePresence>\n        {currentPacket && !isComplete && (\n          <motion.div\n            className=\"data-packet\"\n            initial={{ \n              x: direction === 'down' ? -100 : 600,\n              y: getPacketPosition(currentPacket.currentLayer),\n              scale: 0.8,\n              opacity: 0\n            }}\n            animate={{ \n              x: 500,\n              y: getPacketPosition(currentPacket.currentLayer),\n              scale: 1,\n              opacity: 1\n            }}\n            exit={{ \n              x: direction === 'down' ? 600 : -100,\n              opacity: 0,\n              scale: 0.8\n            }}\n            transition={{ \n              duration: 0.6,\n              ease: \"easeInOut\"\n            }}\n          >\n            <div className=\"packet-content\">\n              <div className=\"packet-data\">\n                {currentPacket.content}\n              </div>\n              \n              <div className=\"packet-headers\">\n                {getHeadersDisplay().map((header, index) => (\n                  <motion.div\n                    key={index}\n                    className=\"packet-header\"\n                    initial={{ opacity: 0, scale: 0.8 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ delay: index * 0.1 }}\n                  >\n                    {header}\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"packet-arrow\">\n              {direction === 'down' ? '⬇️' : '⬆️'}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      <div className=\"flow-info\">\n        <h3>\n          {direction === 'down' ? '📤 数据发送过程' : '📥 数据接收过程'}\n        </h3>\n        <p>\n          {direction === 'down' \n            ? '数据从应用层向下传递，每层添加自己的头部信息'\n            : '数据从物理层向上传递，每层移除对应的头部信息'\n          }\n        </p>\n        \n        {currentPacket && (\n          <div className=\"current-layer-info\">\n            <strong>当前处理层:</strong> \n            {OSI_LAYERS.find(l => l.id === currentPacket.currentLayer)?.name}\n          </div>\n        )}\n      </div>\n\n      <div className=\"process-steps\">\n        {OSI_LAYERS.map((layer, index) => {\n          const stepIndex = direction === 'down' ? 7 - layer.id : layer.id - 1;\n          const isActive = animationStep > stepIndex;\n          const isCurrent = animationStep === stepIndex + 1;\n          \n          return (\n            <motion.div\n              key={layer.id}\n              className={`process-step ${isActive ? 'completed' : ''} ${isCurrent ? 'current' : ''}`}\n              initial={{ opacity: 0.3 }}\n              animate={{ \n                opacity: isActive ? 1 : isCurrent ? 0.8 : 0.3,\n                scale: isCurrent ? 1.1 : 1\n              }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"step-number\">{layer.id}</div>\n              <div className=\"step-name\">{layer.name}</div>\n              {isActive && (\n                <motion.div\n                  className=\"step-checkmark\"\n                  initial={{ scale: 0 }}\n                  animate={{ scale: 1 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  ✓\n                </motion.div>\n              )}\n            </motion.div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default DataFlowAnimation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,UAAU,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc/C,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACzF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAoB,IAAI,CAAC;EAC3E,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMkB,WAAW,GAAGV,SAAS,KAAK,MAAM,GACpC;IAAEW,OAAO,EAAE,cAAc;IAAEC,OAAO,EAAE;EAAG,CAAC,GACxC;IAAED,OAAO,EAAE,eAAe;IAAEC,OAAO,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB;EAAE,CAAC;EAEzFnB,SAAS,CAAC,MAAM;IACd,IAAIe,UAAU,EAAE;IAEhB,MAAMK,MAAM,GAAGb,SAAS,KAAK,MAAM,GAAGJ,UAAU,GAAG,CAAC,GAAGA,UAAU,CAAC,CAACkB,OAAO,CAAC,CAAC;IAC5E,MAAMC,UAAU,GAAGF,MAAM,CAACG,MAAM;IAEhC,IAAIV,aAAa,GAAGS,UAAU,EAAE;MAC9B,MAAME,YAAY,GAAGJ,MAAM,CAACP,aAAa,CAAC;MAC1C,MAAMY,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7Bd,gBAAgB,CAAC;UACfe,EAAE,EAAE,UAAUd,aAAa,EAAE;UAC7BK,OAAO,EAAED,WAAW,CAACC,OAAO;UAC5BM,YAAY,EAAEA,YAAY,CAACG,EAAE;UAC7BR,OAAO,EAAEZ,SAAS,KAAK,MAAM,GACzB,CAAC,GAAGU,WAAW,CAACE,OAAO,EAAE,GAAGK,YAAY,CAACI,IAAI,SAAS,CAAC,GACvDX,WAAW,CAACE,OAAO,CAACU,KAAK,CAAC,CAAC,EAAE,CAAChB,aAAa;QACjD,CAAC,CAAC;QACFC,gBAAgB,CAACgB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACpC,CAAC,EAAE,GAAG,CAAC;MAEP,OAAO,MAAMC,YAAY,CAACN,KAAK,CAAC;IAClC,CAAC,MAAM;MACL,MAAMA,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BV,aAAa,CAAC,IAAI,CAAC;QACnBR,UAAU,CAAC,CAAC;MACd,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMuB,YAAY,CAACN,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACZ,aAAa,EAAEN,SAAS,EAAEC,UAAU,EAAEO,UAAU,EAAEE,WAAW,CAACC,OAAO,EAAED,WAAW,CAACE,OAAO,CAAC,CAAC;EAEhG,MAAMa,iBAAiB,GAAIC,OAAe,IAAK;IAC7C,MAAMC,KAAK,GAAG/B,UAAU,CAACgC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKM,OAAO,CAAC;IACpD,OAAOC,KAAK,GAAGA,KAAK,CAACG,QAAQ,CAACC,CAAC,GAAG,EAAE,GAAG,CAAC;EAC1C,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC5B,aAAa,EAAE,OAAO,EAAE;IAE7B,IAAIJ,SAAS,KAAK,MAAM,EAAE;MACxB;MACA,OAAOI,aAAa,CAACQ,OAAO;IAC9B,CAAC,MAAM;MACL;MACA,OAAOR,aAAa,CAACQ,OAAO;IAC9B;EACF,CAAC;EAED,oBACEd,OAAA;IAAKmC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCpC,OAAA,CAACH,eAAe;MAAAuC,QAAA,EACb9B,aAAa,IAAI,CAACI,UAAU,iBAC3BV,OAAA,CAACJ,MAAM,CAACyC,GAAG;QACTF,SAAS,EAAC,aAAa;QACvBG,OAAO,EAAE;UACPC,CAAC,EAAErC,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG;UACpC+B,CAAC,EAAEN,iBAAiB,CAACrB,aAAa,CAACa,YAAY,CAAC;UAChDqB,KAAK,EAAE,GAAG;UACVC,OAAO,EAAE;QACX,CAAE;QACFC,OAAO,EAAE;UACPH,CAAC,EAAE,GAAG;UACNN,CAAC,EAAEN,iBAAiB,CAACrB,aAAa,CAACa,YAAY,CAAC;UAChDqB,KAAK,EAAE,CAAC;UACRC,OAAO,EAAE;QACX,CAAE;QACFE,IAAI,EAAE;UACJJ,CAAC,EAAErC,SAAS,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG;UACpCuC,OAAO,EAAE,CAAC;UACVD,KAAK,EAAE;QACT,CAAE;QACFI,UAAU,EAAE;UACVC,QAAQ,EAAE,GAAG;UACbC,IAAI,EAAE;QACR,CAAE;QAAAV,QAAA,gBAEFpC,OAAA;UAAKmC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpC,OAAA;YAAKmC,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzB9B,aAAa,CAACO;UAAO;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAENlD,OAAA;YAAKmC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BF,iBAAiB,CAAC,CAAC,CAACiB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACrCrD,OAAA,CAACJ,MAAM,CAACyC,GAAG;cAETF,SAAS,EAAC,eAAe;cACzBG,OAAO,EAAE;gBAAEG,OAAO,EAAE,CAAC;gBAAED,KAAK,EAAE;cAAI,CAAE;cACpCE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,KAAK,EAAE;cAAE,CAAE;cAClCI,UAAU,EAAE;gBAAEU,KAAK,EAAED,KAAK,GAAG;cAAI,CAAE;cAAAjB,QAAA,EAElCgB;YAAM,GANFC,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA;UAAKmC,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BlC,SAAS,KAAK,MAAM,GAAG,IAAI,GAAG;QAAI;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAElBlD,OAAA;MAAKmC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBpC,OAAA;QAAAoC,QAAA,EACGlC,SAAS,KAAK,MAAM,GAAG,WAAW,GAAG;MAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACLlD,OAAA;QAAAoC,QAAA,EACGlC,SAAS,KAAK,MAAM,GACjB,wBAAwB,GACxB;MAAwB;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE3B,CAAC,EAEH5C,aAAa,iBACZN,OAAA;QAAKmC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCpC,OAAA;UAAAoC,QAAA,EAAQ;QAAM;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,GAAA7C,gBAAA,GACtBP,UAAU,CAACgC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKhB,aAAa,CAACa,YAAY,CAAC,cAAAd,gBAAA,uBAAzDA,gBAAA,CAA2DkB,IAAI;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENlD,OAAA;MAAKmC,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BtC,UAAU,CAACqD,GAAG,CAAC,CAACtB,KAAK,EAAEwB,KAAK,KAAK;QAChC,MAAME,SAAS,GAAGrD,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG2B,KAAK,CAACP,EAAE,GAAGO,KAAK,CAACP,EAAE,GAAG,CAAC;QACpE,MAAMkC,QAAQ,GAAGhD,aAAa,GAAG+C,SAAS;QAC1C,MAAME,SAAS,GAAGjD,aAAa,KAAK+C,SAAS,GAAG,CAAC;QAEjD,oBACEvD,OAAA,CAACJ,MAAM,CAACyC,GAAG;UAETF,SAAS,EAAE,gBAAgBqB,QAAQ,GAAG,WAAW,GAAG,EAAE,IAAIC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;UACvFnB,OAAO,EAAE;YAAEG,OAAO,EAAE;UAAI,CAAE;UAC1BC,OAAO,EAAE;YACPD,OAAO,EAAEe,QAAQ,GAAG,CAAC,GAAGC,SAAS,GAAG,GAAG,GAAG,GAAG;YAC7CjB,KAAK,EAAEiB,SAAS,GAAG,GAAG,GAAG;UAC3B,CAAE;UACFb,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAT,QAAA,gBAE9BpC,OAAA;YAAKmC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEP,KAAK,CAACP;UAAE;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7ClD,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEP,KAAK,CAACN;UAAI;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC5CM,QAAQ,iBACPxD,OAAA,CAACJ,MAAM,CAACyC,GAAG;YACTF,SAAS,EAAC,gBAAgB;YAC1BG,OAAO,EAAE;cAAEE,KAAK,EAAE;YAAE,CAAE;YACtBE,OAAO,EAAE;cAAEF,KAAK,EAAE;YAAE,CAAE;YACtBI,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAT,QAAA,EAC/B;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA,GApBIrB,KAAK,CAACP,EAAE;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBH,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CArKIH,iBAAmD;AAAAyD,EAAA,GAAnDzD,iBAAmD;AAuKzD,eAAeA,iBAAiB;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}