{"ast": null, "code": "function mixImmediate(a, b) {\n  return p => p > 0 ? b : a;\n}\nexport { mixImmediate };", "map": {"version": 3, "names": ["mixImmediate", "a", "b", "p"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/utils/mix/immediate.mjs"], "sourcesContent": ["function mixImmediate(a, b) {\n    return (p) => (p > 0 ? b : a);\n}\n\nexport { mixImmediate };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxB,OAAQC,CAAC,IAAMA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGD,CAAE;AACjC;AAEA,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}