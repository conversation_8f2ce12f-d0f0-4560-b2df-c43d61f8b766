{"ast": null, "code": "import { fillOffset } from './fill.mjs';\nfunction defaultOffset(arr) {\n  const offset = [0];\n  fillOffset(offset, arr.length - 1);\n  return offset;\n}\nexport { defaultOffset };", "map": {"version": 3, "names": ["fillOffset", "defaultOffset", "arr", "offset", "length"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/animation/keyframes/offsets/default.mjs"], "sourcesContent": ["import { fillOffset } from './fill.mjs';\n\nfunction defaultOffset(arr) {\n    const offset = [0];\n    fillOffset(offset, arr.length - 1);\n    return offset;\n}\n\nexport { defaultOffset };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,YAAY;AAEvC,SAASC,aAAaA,CAACC,GAAG,EAAE;EACxB,MAAMC,MAAM,GAAG,CAAC,CAAC,CAAC;EAClBH,UAAU,CAACG,MAAM,EAAED,GAAG,CAACE,MAAM,GAAG,CAAC,CAAC;EAClC,OAAOD,MAAM;AACjB;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}