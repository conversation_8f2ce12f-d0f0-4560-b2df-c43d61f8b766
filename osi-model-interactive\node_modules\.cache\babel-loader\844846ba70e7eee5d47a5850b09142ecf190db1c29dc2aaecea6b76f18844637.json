{"ast": null, "code": "import { GroupAnimation } from './GroupAnimation.mjs';\nclass GroupAnimationWithThen extends GroupAnimation {\n  then(onResolve, _onReject) {\n    return this.finished.finally(onResolve).then(() => {});\n  }\n}\nexport { GroupAnimationWithThen };", "map": {"version": 3, "names": ["GroupAnimation", "GroupAnimationWithThen", "then", "onResolve", "_onReject", "finished", "finally"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs"], "sourcesContent": ["import { GroupAnimation } from './GroupAnimation.mjs';\n\nclass GroupAnimationWithThen extends GroupAnimation {\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n}\n\nexport { GroupAnimationWithThen };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAsB;AAErD,MAAMC,sBAAsB,SAASD,cAAc,CAAC;EAChDE,IAAIA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACvB,OAAO,IAAI,CAACC,QAAQ,CAACC,OAAO,CAACH,SAAS,CAAC,CAACD,IAAI,CAAC,MAAM,CAAE,CAAC,CAAC;EAC3D;AACJ;AAEA,SAASD,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}