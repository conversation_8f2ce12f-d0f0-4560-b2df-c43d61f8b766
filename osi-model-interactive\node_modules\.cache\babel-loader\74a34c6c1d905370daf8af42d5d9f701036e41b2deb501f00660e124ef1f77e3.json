{"ast": null, "code": "import { animations } from '../../../motion/features/animations.mjs';\nimport { drag } from '../../../motion/features/drag.mjs';\nimport { gestureAnimations } from '../../../motion/features/gestures.mjs';\nimport { layout } from '../../../motion/features/layout.mjs';\nconst featureBundle = {\n  ...animations,\n  ...gestureAnimations,\n  ...drag,\n  ...layout\n};\nexport { featureBundle };", "map": {"version": 3, "names": ["animations", "drag", "gestureAnimations", "layout", "featureBundle"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/render/components/motion/feature-bundle.mjs"], "sourcesContent": ["import { animations } from '../../../motion/features/animations.mjs';\nimport { drag } from '../../../motion/features/drag.mjs';\nimport { gestureAnimations } from '../../../motion/features/gestures.mjs';\nimport { layout } from '../../../motion/features/layout.mjs';\n\nconst featureBundle = {\n    ...animations,\n    ...gestureAnimations,\n    ...drag,\n    ...layout,\n};\n\nexport { featureBundle };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,yCAAyC;AACpE,SAASC,IAAI,QAAQ,mCAAmC;AACxD,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,MAAM,QAAQ,qCAAqC;AAE5D,MAAMC,aAAa,GAAG;EAClB,GAAGJ,UAAU;EACb,GAAGE,iBAAiB;EACpB,GAAGD,IAAI;EACP,GAAGE;AACP,CAAC;AAED,SAASC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}