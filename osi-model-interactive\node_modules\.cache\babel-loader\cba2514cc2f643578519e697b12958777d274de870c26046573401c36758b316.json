{"ast": null, "code": "function formatErrorMessage(message, errorCode) {\n  return errorCode ? `${message}. For more information and steps for solving, visit https://motion.dev/troubleshooting/${errorCode}` : message;\n}\nexport { formatErrorMessage };", "map": {"version": 3, "names": ["formatErrorMessage", "message", "errorCode"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-utils/dist/es/format-error-message.mjs"], "sourcesContent": ["function formatErrorMessage(message, errorCode) {\n    return errorCode\n        ? `${message}. For more information and steps for solving, visit https://motion.dev/troubleshooting/${errorCode}`\n        : message;\n}\n\nexport { formatErrorMessage };\n"], "mappings": "AAAA,SAASA,kBAAkBA,CAACC,OAAO,EAAEC,SAAS,EAAE;EAC5C,OAAOA,SAAS,GACV,GAAGD,OAAO,0FAA0FC,SAAS,EAAE,GAC/GD,OAAO;AACjB;AAEA,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}