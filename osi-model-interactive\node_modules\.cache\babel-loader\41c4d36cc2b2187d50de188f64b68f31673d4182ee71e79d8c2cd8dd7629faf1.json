{"ast": null, "code": "\"use client\";\n\nimport { jsx } from 'react/jsx-runtime';\nimport { invariant } from 'motion-utils';\nimport { forwardRef, useRef, useEffect } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/components/motion/proxy.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { checkReorder } from './utils/check-reorder.mjs';\nfunction ReorderGroupComponent({\n  children,\n  as = \"ul\",\n  axis = \"y\",\n  onReorder,\n  values,\n  ...props\n}, externalRef) {\n  const Component = useConstant(() => motion[as]);\n  const order = [];\n  const isReordering = useRef(false);\n  invariant(Boolean(values), \"Reorder.Group must be provided a values prop\", \"reorder-values\");\n  const context = {\n    axis,\n    registerItem: (value, layout) => {\n      // If the entry was already added, update it rather than adding it again\n      const idx = order.findIndex(entry => value === entry.value);\n      if (idx !== -1) {\n        order[idx].layout = layout[axis];\n      } else {\n        order.push({\n          value: value,\n          layout: layout[axis]\n        });\n      }\n      order.sort(compareMin);\n    },\n    updateOrder: (item, offset, velocity) => {\n      if (isReordering.current) return;\n      const newOrder = checkReorder(order, item, offset, velocity);\n      if (order !== newOrder) {\n        isReordering.current = true;\n        onReorder(newOrder.map(getValue).filter(value => values.indexOf(value) !== -1));\n      }\n    }\n  };\n  useEffect(() => {\n    isReordering.current = false;\n  });\n  return jsx(Component, {\n    ...props,\n    ref: externalRef,\n    ignoreStrict: true,\n    children: jsx(ReorderContext.Provider, {\n      value: context,\n      children: children\n    })\n  });\n}\nconst ReorderGroup = /*@__PURE__*/forwardRef(ReorderGroupComponent);\nfunction getValue(item) {\n  return item.value;\n}\nfunction compareMin(a, b) {\n  return a.layout.min - b.layout.min;\n}\nexport { ReorderGroup, ReorderGroupComponent };", "map": {"version": 3, "names": ["jsx", "invariant", "forwardRef", "useRef", "useEffect", "ReorderContext", "motion", "useConstant", "check<PERSON>eor<PERSON>", "ReorderGroupComponent", "children", "as", "axis", "onReorder", "values", "props", "externalRef", "Component", "order", "isReordering", "Boolean", "context", "registerItem", "value", "layout", "idx", "findIndex", "entry", "push", "sort", "compareMin", "updateOrder", "item", "offset", "velocity", "current", "newOrder", "map", "getValue", "filter", "indexOf", "ref", "ignoreStrict", "Provider", "ReorderGroup", "a", "b", "min"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/components/Reorder/Group.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { invariant } from 'motion-utils';\nimport { forwardRef, useRef, useEffect } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/components/motion/proxy.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { checkReorder } from './utils/check-reorder.mjs';\n\nfunction ReorderGroupComponent({ children, as = \"ul\", axis = \"y\", onReorder, values, ...props }, externalRef) {\n    const Component = useConstant(() => motion[as]);\n    const order = [];\n    const isReordering = useRef(false);\n    invariant(Boolean(values), \"Reorder.Group must be provided a values prop\", \"reorder-values\");\n    const context = {\n        axis,\n        registerItem: (value, layout) => {\n            // If the entry was already added, update it rather than adding it again\n            const idx = order.findIndex((entry) => value === entry.value);\n            if (idx !== -1) {\n                order[idx].layout = layout[axis];\n            }\n            else {\n                order.push({ value: value, layout: layout[axis] });\n            }\n            order.sort(compareMin);\n        },\n        updateOrder: (item, offset, velocity) => {\n            if (isReordering.current)\n                return;\n            const newOrder = checkReorder(order, item, offset, velocity);\n            if (order !== newOrder) {\n                isReordering.current = true;\n                onReorder(newOrder\n                    .map(getValue)\n                    .filter((value) => values.indexOf(value) !== -1));\n            }\n        },\n    };\n    useEffect(() => {\n        isReordering.current = false;\n    });\n    return (jsx(Component, { ...props, ref: externalRef, ignoreStrict: true, children: jsx(ReorderContext.Provider, { value: context, children: children }) }));\n}\nconst ReorderGroup = /*@__PURE__*/ forwardRef(ReorderGroupComponent);\nfunction getValue(item) {\n    return item.value;\n}\nfunction compareMin(a, b) {\n    return a.layout.min - b.layout.min;\n}\n\nexport { ReorderGroup, ReorderGroupComponent };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,UAAU,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACrD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,0CAA0C;AACjE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AAExD,SAASC,qBAAqBA,CAAC;EAAEC,QAAQ;EAAEC,EAAE,GAAG,IAAI;EAAEC,IAAI,GAAG,GAAG;EAAEC,SAAS;EAAEC,MAAM;EAAE,GAAGC;AAAM,CAAC,EAAEC,WAAW,EAAE;EAC1G,MAAMC,SAAS,GAAGV,WAAW,CAAC,MAAMD,MAAM,CAACK,EAAE,CAAC,CAAC;EAC/C,MAAMO,KAAK,GAAG,EAAE;EAChB,MAAMC,YAAY,GAAGhB,MAAM,CAAC,KAAK,CAAC;EAClCF,SAAS,CAACmB,OAAO,CAACN,MAAM,CAAC,EAAE,8CAA8C,EAAE,gBAAgB,CAAC;EAC5F,MAAMO,OAAO,GAAG;IACZT,IAAI;IACJU,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC7B;MACA,MAAMC,GAAG,GAAGP,KAAK,CAACQ,SAAS,CAAEC,KAAK,IAAKJ,KAAK,KAAKI,KAAK,CAACJ,KAAK,CAAC;MAC7D,IAAIE,GAAG,KAAK,CAAC,CAAC,EAAE;QACZP,KAAK,CAACO,GAAG,CAAC,CAACD,MAAM,GAAGA,MAAM,CAACZ,IAAI,CAAC;MACpC,CAAC,MACI;QACDM,KAAK,CAACU,IAAI,CAAC;UAAEL,KAAK,EAAEA,KAAK;UAAEC,MAAM,EAAEA,MAAM,CAACZ,IAAI;QAAE,CAAC,CAAC;MACtD;MACAM,KAAK,CAACW,IAAI,CAACC,UAAU,CAAC;IAC1B,CAAC;IACDC,WAAW,EAAEA,CAACC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,KAAK;MACrC,IAAIf,YAAY,CAACgB,OAAO,EACpB;MACJ,MAAMC,QAAQ,GAAG5B,YAAY,CAACU,KAAK,EAAEc,IAAI,EAAEC,MAAM,EAAEC,QAAQ,CAAC;MAC5D,IAAIhB,KAAK,KAAKkB,QAAQ,EAAE;QACpBjB,YAAY,CAACgB,OAAO,GAAG,IAAI;QAC3BtB,SAAS,CAACuB,QAAQ,CACbC,GAAG,CAACC,QAAQ,CAAC,CACbC,MAAM,CAAEhB,KAAK,IAAKT,MAAM,CAAC0B,OAAO,CAACjB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;MACzD;IACJ;EACJ,CAAC;EACDnB,SAAS,CAAC,MAAM;IACZe,YAAY,CAACgB,OAAO,GAAG,KAAK;EAChC,CAAC,CAAC;EACF,OAAQnC,GAAG,CAACiB,SAAS,EAAE;IAAE,GAAGF,KAAK;IAAE0B,GAAG,EAAEzB,WAAW;IAAE0B,YAAY,EAAE,IAAI;IAAEhC,QAAQ,EAAEV,GAAG,CAACK,cAAc,CAACsC,QAAQ,EAAE;MAAEpB,KAAK,EAAEF,OAAO;MAAEX,QAAQ,EAAEA;IAAS,CAAC;EAAE,CAAC,CAAC;AAC9J;AACA,MAAMkC,YAAY,GAAG,aAAc1C,UAAU,CAACO,qBAAqB,CAAC;AACpE,SAAS6B,QAAQA,CAACN,IAAI,EAAE;EACpB,OAAOA,IAAI,CAACT,KAAK;AACrB;AACA,SAASO,UAAUA,CAACe,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOD,CAAC,CAACrB,MAAM,CAACuB,GAAG,GAAGD,CAAC,CAACtB,MAAM,CAACuB,GAAG;AACtC;AAEA,SAASH,YAAY,EAAEnC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}