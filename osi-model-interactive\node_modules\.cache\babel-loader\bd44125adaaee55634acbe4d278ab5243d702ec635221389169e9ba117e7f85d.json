{"ast": null, "code": "import { resolveVariantFromProps } from './resolve-variants.mjs';\nfunction resolveVariant(visualElement, definition, custom) {\n  const props = visualElement.getProps();\n  return resolveVariantFromProps(props, definition, custom !== undefined ? custom : props.custom, visualElement);\n}\nexport { resolveVariant };", "map": {"version": 3, "names": ["resolveVariantFromProps", "resolveV<PERSON>t", "visualElement", "definition", "custom", "props", "getProps", "undefined"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs"], "sourcesContent": ["import { resolveVariantFromProps } from './resolve-variants.mjs';\n\nfunction resolveVariant(visualElement, definition, custom) {\n    const props = visualElement.getProps();\n    return resolveVariantFromProps(props, definition, custom !== undefined ? custom : props.custom, visualElement);\n}\n\nexport { resolveVariant };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,wBAAwB;AAEhE,SAASC,cAAcA,CAACC,aAAa,EAAEC,UAAU,EAAEC,MAAM,EAAE;EACvD,MAAMC,KAAK,GAAGH,aAAa,CAACI,QAAQ,CAAC,CAAC;EACtC,OAAON,uBAAuB,CAACK,KAAK,EAAEF,UAAU,EAAEC,MAAM,KAAKG,SAAS,GAAGH,MAAM,GAAGC,KAAK,CAACD,MAAM,EAAEF,aAAa,CAAC;AAClH;AAEA,SAASD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}