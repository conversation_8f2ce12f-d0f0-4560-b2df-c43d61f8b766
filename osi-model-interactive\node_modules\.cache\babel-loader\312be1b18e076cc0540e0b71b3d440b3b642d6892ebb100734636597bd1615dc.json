{"ast": null, "code": "/*#__NO_SIDE_EFFECTS__*/\nconst createUnitType = unit => ({\n  test: v => typeof v === \"string\" && v.endsWith(unit) && v.split(\" \").length === 1,\n  parse: parseFloat,\n  transform: v => `${v}${unit}`\n});\nconst degrees = /*@__PURE__*/createUnitType(\"deg\");\nconst percent = /*@__PURE__*/createUnitType(\"%\");\nconst px = /*@__PURE__*/createUnitType(\"px\");\nconst vh = /*@__PURE__*/createUnitType(\"vh\");\nconst vw = /*@__PURE__*/createUnitType(\"vw\");\nconst progressPercentage = /*@__PURE__*/(() => ({\n  ...percent,\n  parse: v => percent.parse(v) / 100,\n  transform: v => percent.transform(v * 100)\n}))();\nexport { degrees, percent, progressPercentage, px, vh, vw };", "map": {"version": 3, "names": ["createUnitType", "unit", "test", "v", "endsWith", "split", "length", "parse", "parseFloat", "transform", "degrees", "percent", "px", "vh", "vw", "progressPercentage"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/value/types/numbers/units.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst createUnitType = (unit) => ({\n    test: (v) => typeof v === \"string\" && v.endsWith(unit) && v.split(\" \").length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = /*@__PURE__*/ createUnitType(\"deg\");\nconst percent = /*@__PURE__*/ createUnitType(\"%\");\nconst px = /*@__PURE__*/ createUnitType(\"px\");\nconst vh = /*@__PURE__*/ createUnitType(\"vh\");\nconst vw = /*@__PURE__*/ createUnitType(\"vw\");\nconst progressPercentage = /*@__PURE__*/ (() => ({\n    ...percent,\n    parse: (v) => percent.parse(v) / 100,\n    transform: (v) => percent.transform(v * 100),\n}))();\n\nexport { degrees, percent, progressPercentage, px, vh, vw };\n"], "mappings": "AAAA;AACA,MAAMA,cAAc,GAAIC,IAAI,KAAM;EAC9BC,IAAI,EAAGC,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,QAAQ,CAACH,IAAI,CAAC,IAAIE,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,KAAK,CAAC;EACnFC,KAAK,EAAEC,UAAU;EACjBC,SAAS,EAAGN,CAAC,IAAK,GAAGA,CAAC,GAAGF,IAAI;AACjC,CAAC,CAAC;AACF,MAAMS,OAAO,GAAG,aAAcV,cAAc,CAAC,KAAK,CAAC;AACnD,MAAMW,OAAO,GAAG,aAAcX,cAAc,CAAC,GAAG,CAAC;AACjD,MAAMY,EAAE,GAAG,aAAcZ,cAAc,CAAC,IAAI,CAAC;AAC7C,MAAMa,EAAE,GAAG,aAAcb,cAAc,CAAC,IAAI,CAAC;AAC7C,MAAMc,EAAE,GAAG,aAAcd,cAAc,CAAC,IAAI,CAAC;AAC7C,MAAMe,kBAAkB,GAAG,aAAc,CAAC,OAAO;EAC7C,GAAGJ,OAAO;EACVJ,KAAK,EAAGJ,CAAC,IAAKQ,OAAO,CAACJ,KAAK,CAACJ,CAAC,CAAC,GAAG,GAAG;EACpCM,SAAS,EAAGN,CAAC,IAAKQ,OAAO,CAACF,SAAS,CAACN,CAAC,GAAG,GAAG;AAC/C,CAAC,CAAC,EAAE,CAAC;AAEL,SAASO,OAAO,EAAEC,OAAO,EAAEI,kBAAkB,EAAEH,EAAE,EAAEC,EAAE,EAAEC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}