{"ast": null, "code": "import { resolveElements } from '../../utils/resolve-elements.mjs';\nfunction setupGesture(elementOrSelector, options) {\n  const elements = resolveElements(elementOrSelector);\n  const gestureAbortController = new AbortController();\n  const eventOptions = {\n    passive: true,\n    ...options,\n    signal: gestureAbortController.signal\n  };\n  const cancel = () => gestureAbortController.abort();\n  return [elements, eventOptions, cancel];\n}\nexport { setupGesture };", "map": {"version": 3, "names": ["resolveElements", "setupGesture", "elementOrSelector", "options", "elements", "gestureAbortController", "AbortController", "eventOptions", "passive", "signal", "cancel", "abort"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs"], "sourcesContent": ["import { resolveElements } from '../../utils/resolve-elements.mjs';\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = resolveElements(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\nexport { setupGesture };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,kCAAkC;AAElE,SAASC,YAAYA,CAACC,iBAAiB,EAAEC,OAAO,EAAE;EAC9C,MAAMC,QAAQ,GAAGJ,eAAe,CAACE,iBAAiB,CAAC;EACnD,MAAMG,sBAAsB,GAAG,IAAIC,eAAe,CAAC,CAAC;EACpD,MAAMC,YAAY,GAAG;IACjBC,OAAO,EAAE,IAAI;IACb,GAAGL,OAAO;IACVM,MAAM,EAAEJ,sBAAsB,CAACI;EACnC,CAAC;EACD,MAAMC,MAAM,GAAGA,CAAA,KAAML,sBAAsB,CAACM,KAAK,CAAC,CAAC;EACnD,OAAO,CAACP,QAAQ,EAAEG,YAAY,EAAEG,MAAM,CAAC;AAC3C;AAEA,SAAST,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}