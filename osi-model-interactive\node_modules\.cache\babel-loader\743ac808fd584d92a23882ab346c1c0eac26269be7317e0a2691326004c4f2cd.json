{"ast": null, "code": "const isPressing = new WeakSet();\nexport { isPressing };", "map": {"version": 3, "names": ["isPressing", "WeakSet"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs"], "sourcesContent": ["const isPressing = new WeakSet();\n\nexport { isPressing };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG,IAAIC,OAAO,CAAC,CAAC;AAEhC,SAASD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}