{"ast": null, "code": "// If this number is a decimal, make it just five decimal places\n// to avoid exponents\nconst sanitize = v => Math.round(v * 100000) / 100000;\nexport { sanitize };", "map": {"version": 3, "names": ["sanitize", "v", "Math", "round"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/value/types/utils/sanitize.mjs"], "sourcesContent": ["// If this number is a decimal, make it just five decimal places\n// to avoid exponents\nconst sanitize = (v) => Math.round(v * 100000) / 100000;\n\nexport { sanitize };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,QAAQ,GAAIC,CAAC,IAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM;AAEvD,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}