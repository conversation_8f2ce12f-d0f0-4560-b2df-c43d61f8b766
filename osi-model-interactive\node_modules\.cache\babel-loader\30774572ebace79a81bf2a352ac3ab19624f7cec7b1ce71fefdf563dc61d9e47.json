{"ast": null, "code": "import { number } from './numbers/index.mjs';\nconst int = {\n  ...number,\n  transform: Math.round\n};\nexport { int };", "map": {"version": 3, "names": ["number", "int", "transform", "Math", "round"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/value/types/int.mjs"], "sourcesContent": ["import { number } from './numbers/index.mjs';\n\nconst int = {\n    ...number,\n    transform: Math.round,\n};\n\nexport { int };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,qBAAqB;AAE5C,MAAMC,GAAG,GAAG;EACR,GAAGD,MAAM;EACTE,SAAS,EAAEC,IAAI,CAACC;AACpB,CAAC;AAED,SAASH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}