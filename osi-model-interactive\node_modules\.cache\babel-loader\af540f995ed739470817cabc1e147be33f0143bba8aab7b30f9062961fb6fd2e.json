{"ast": null, "code": "import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attributes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, {\n  attrX,\n  attrY,\n  attrScale,\n  pathLength,\n  pathSpacing = 1,\n  pathOffset = 0,\n  // This is object creation, which we try to avoid per-frame.\n  ...latest\n}, isSVGTag, transformTemplate, styleProp) {\n  buildHTMLStyles(state, latest, transformTemplate);\n  /**\n   * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n   * as normal HTML tags.\n   */\n  if (isSVGTag) {\n    if (state.style.viewBox) {\n      state.attrs.viewBox = state.style.viewBox;\n    }\n    return;\n  }\n  state.attrs = state.style;\n  state.style = {};\n  const {\n    attrs,\n    style\n  } = state;\n  /**\n   * However, we apply transforms as CSS transforms.\n   * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n   */\n  if (attrs.transform) {\n    style.transform = attrs.transform;\n    delete attrs.transform;\n  }\n  if (style.transform || attrs.transformOrigin) {\n    style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n    delete attrs.transformOrigin;\n  }\n  if (style.transform) {\n    /**\n     * SVG's element transform-origin uses its own median as a reference.\n     * Therefore, transformBox becomes a fill-box\n     */\n    style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n    delete attrs.transformBox;\n  }\n  // Render attrX/attrY/attrScale as attributes\n  if (attrX !== undefined) attrs.x = attrX;\n  if (attrY !== undefined) attrs.y = attrY;\n  if (attrScale !== undefined) attrs.scale = attrScale;\n  // Build SVG path if one has been defined\n  if (pathLength !== undefined) {\n    buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n  }\n}\nexport { buildSVGAttrs };", "map": {"version": 3, "names": ["buildHTMLStyles", "buildSVGPath", "buildSVGAttrs", "state", "attrX", "attrY", "attrScale", "<PERSON><PERSON><PERSON><PERSON>", "pathSpacing", "pathOffset", "latest", "isSVGTag", "transformTemplate", "styleProp", "style", "viewBox", "attrs", "transform", "transform<PERSON><PERSON>in", "transformBox", "undefined", "x", "y", "scale"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs"], "sourcesContent": ["import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attributes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, isSVGTag, transformTemplate, styleProp) {\n    buildHTMLStyles(state, latest, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style } = state;\n    /**\n     * However, we apply transforms as CSS transforms.\n     * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n     */\n    if (attrs.transform) {\n        style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    if (style.transform || attrs.transformOrigin) {\n        style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n        delete attrs.transformOrigin;\n    }\n    if (style.transform) {\n        /**\n         * SVG's element transform-origin uses its own median as a reference.\n         * Therefore, transformBox becomes a fill-box\n         */\n        style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n        delete attrs.transformBox;\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\nexport { buildSVGAttrs };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,mCAAmC;AACnE,SAASC,YAAY,QAAQ,YAAY;;AAEzC;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAAEC,KAAK;EAAEC,KAAK;EAAEC,SAAS;EAAEC,UAAU;EAAEC,WAAW,GAAG,CAAC;EAAEC,UAAU,GAAG,CAAC;EACpG;EACA,GAAGC;AAAO,CAAC,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,SAAS,EAAE;EACjDb,eAAe,CAACG,KAAK,EAAEO,MAAM,EAAEE,iBAAiB,CAAC;EACjD;AACJ;AACA;AACA;EACI,IAAID,QAAQ,EAAE;IACV,IAAIR,KAAK,CAACW,KAAK,CAACC,OAAO,EAAE;MACrBZ,KAAK,CAACa,KAAK,CAACD,OAAO,GAAGZ,KAAK,CAACW,KAAK,CAACC,OAAO;IAC7C;IACA;EACJ;EACAZ,KAAK,CAACa,KAAK,GAAGb,KAAK,CAACW,KAAK;EACzBX,KAAK,CAACW,KAAK,GAAG,CAAC,CAAC;EAChB,MAAM;IAAEE,KAAK;IAAEF;EAAM,CAAC,GAAGX,KAAK;EAC9B;AACJ;AACA;AACA;EACI,IAAIa,KAAK,CAACC,SAAS,EAAE;IACjBH,KAAK,CAACG,SAAS,GAAGD,KAAK,CAACC,SAAS;IACjC,OAAOD,KAAK,CAACC,SAAS;EAC1B;EACA,IAAIH,KAAK,CAACG,SAAS,IAAID,KAAK,CAACE,eAAe,EAAE;IAC1CJ,KAAK,CAACI,eAAe,GAAGF,KAAK,CAACE,eAAe,IAAI,SAAS;IAC1D,OAAOF,KAAK,CAACE,eAAe;EAChC;EACA,IAAIJ,KAAK,CAACG,SAAS,EAAE;IACjB;AACR;AACA;AACA;IACQH,KAAK,CAACK,YAAY,GAAGN,SAAS,EAAEM,YAAY,IAAI,UAAU;IAC1D,OAAOH,KAAK,CAACG,YAAY;EAC7B;EACA;EACA,IAAIf,KAAK,KAAKgB,SAAS,EACnBJ,KAAK,CAACK,CAAC,GAAGjB,KAAK;EACnB,IAAIC,KAAK,KAAKe,SAAS,EACnBJ,KAAK,CAACM,CAAC,GAAGjB,KAAK;EACnB,IAAIC,SAAS,KAAKc,SAAS,EACvBJ,KAAK,CAACO,KAAK,GAAGjB,SAAS;EAC3B;EACA,IAAIC,UAAU,KAAKa,SAAS,EAAE;IAC1BnB,YAAY,CAACe,KAAK,EAAET,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAE,KAAK,CAAC;EACnE;AACJ;AAEA,SAASP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}