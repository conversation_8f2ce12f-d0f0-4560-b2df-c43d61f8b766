{"ast": null, "code": "const isNotNull = value => value !== null;\nfunction getFinalKeyframe(keyframes, {\n  repeat,\n  repeatType = \"loop\"\n}, finalKeyframe, speed = 1) {\n  const resolvedKeyframes = keyframes.filter(isNotNull);\n  const useFirstKeyframe = speed < 0 || repeat && repeatType !== \"loop\" && repeat % 2 === 1;\n  const index = useFirstKeyframe ? 0 : resolvedKeyframes.length - 1;\n  return !index || finalKeyframe === undefined ? resolvedKeyframes[index] : finalKeyframe;\n}\nexport { getFinalKeyframe };", "map": {"version": 3, "names": ["isNotNull", "value", "getFinalKeyframe", "keyframes", "repeat", "repeatType", "finalKeyframe", "speed", "resolvedKeyframes", "filter", "useFirstKeyframe", "index", "length", "undefined"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs"], "sourcesContent": ["const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe, speed = 1) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const useFirstKeyframe = speed < 0 || (repeat && repeatType !== \"loop\" && repeat % 2 === 1);\n    const index = useFirstKeyframe ? 0 : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAIC,KAAK,IAAKA,KAAK,KAAK,IAAI;AAC3C,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EAAEC,MAAM;EAAEC,UAAU,GAAG;AAAO,CAAC,EAAEC,aAAa,EAAEC,KAAK,GAAG,CAAC,EAAE;EAC5F,MAAMC,iBAAiB,GAAGL,SAAS,CAACM,MAAM,CAACT,SAAS,CAAC;EACrD,MAAMU,gBAAgB,GAAGH,KAAK,GAAG,CAAC,IAAKH,MAAM,IAAIC,UAAU,KAAK,MAAM,IAAID,MAAM,GAAG,CAAC,KAAK,CAAE;EAC3F,MAAMO,KAAK,GAAGD,gBAAgB,GAAG,CAAC,GAAGF,iBAAiB,CAACI,MAAM,GAAG,CAAC;EACjE,OAAO,CAACD,KAAK,IAAIL,aAAa,KAAKO,SAAS,GACtCL,iBAAiB,CAACG,KAAK,CAAC,GACxBL,aAAa;AACvB;AAEA,SAASJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}