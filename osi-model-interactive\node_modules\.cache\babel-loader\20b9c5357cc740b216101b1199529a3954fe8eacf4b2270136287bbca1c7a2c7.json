{"ast": null, "code": "const windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n  windowResizeHandler = () => {\n    const info = {\n      get width() {\n        return window.innerWidth;\n      },\n      get height() {\n        return window.innerHeight;\n      }\n    };\n    windowCallbacks.forEach(callback => callback(info));\n  };\n  window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n  windowCallbacks.add(callback);\n  if (!windowResizeHandler) createWindowResizeHandler();\n  return () => {\n    windowCallbacks.delete(callback);\n    if (!windowCallbacks.size && typeof windowResizeHandler === \"function\") {\n      window.removeEventListener(\"resize\", windowResizeHandler);\n      windowResizeHandler = undefined;\n    }\n  };\n}\nexport { resizeWindow };", "map": {"version": 3, "names": ["windowCallbacks", "Set", "windowResizeHandler", "createWindowResizeHandler", "info", "width", "window", "innerWidth", "height", "innerHeight", "for<PERSON>ach", "callback", "addEventListener", "resizeWindow", "add", "delete", "size", "removeEventListener", "undefined"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/resize/handle-window.mjs"], "sourcesContent": ["const windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const info = {\n            get width() {\n                return window.innerWidth;\n            },\n            get height() {\n                return window.innerHeight;\n            },\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size &&\n            typeof windowResizeHandler === \"function\") {\n            window.removeEventListener(\"resize\", windowResizeHandler);\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\nexport { resizeWindow };\n"], "mappings": "AAAA,MAAMA,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AACjC,IAAIC,mBAAmB;AACvB,SAASC,yBAAyBA,CAAA,EAAG;EACjCD,mBAAmB,GAAGA,CAAA,KAAM;IACxB,MAAME,IAAI,GAAG;MACT,IAAIC,KAAKA,CAAA,EAAG;QACR,OAAOC,MAAM,CAACC,UAAU;MAC5B,CAAC;MACD,IAAIC,MAAMA,CAAA,EAAG;QACT,OAAOF,MAAM,CAACG,WAAW;MAC7B;IACJ,CAAC;IACDT,eAAe,CAACU,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAACP,IAAI,CAAC,CAAC;EACzD,CAAC;EACDE,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAEV,mBAAmB,CAAC;AAC1D;AACA,SAASW,YAAYA,CAACF,QAAQ,EAAE;EAC5BX,eAAe,CAACc,GAAG,CAACH,QAAQ,CAAC;EAC7B,IAAI,CAACT,mBAAmB,EACpBC,yBAAyB,CAAC,CAAC;EAC/B,OAAO,MAAM;IACTH,eAAe,CAACe,MAAM,CAACJ,QAAQ,CAAC;IAChC,IAAI,CAACX,eAAe,CAACgB,IAAI,IACrB,OAAOd,mBAAmB,KAAK,UAAU,EAAE;MAC3CI,MAAM,CAACW,mBAAmB,CAAC,QAAQ,EAAEf,mBAAmB,CAAC;MACzDA,mBAAmB,GAAGgB,SAAS;IACnC;EACJ,CAAC;AACL;AAEA,SAASL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}