[{"C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\components\\OSIVisualization.tsx": "4", "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\components\\OSILayer.tsx": "5", "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\components\\DetailPanel.tsx": "6", "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\components\\DataFlowAnimation.tsx": "7", "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\types\\OSIModel.ts": "8"}, {"size": 554, "mtime": 1753358874073, "results": "9", "hashOfConfig": "10"}, {"size": 425, "mtime": 1753358873645, "results": "11", "hashOfConfig": "10"}, {"size": 230, "mtime": 1753359111523, "results": "12", "hashOfConfig": "10"}, {"size": 5024, "mtime": 1753359409051, "results": "13", "hashOfConfig": "10"}, {"size": 2447, "mtime": 1753359022556, "results": "14", "hashOfConfig": "10"}, {"size": 3697, "mtime": 1753359065997, "results": "15", "hashOfConfig": "10"}, {"size": 5855, "mtime": 1753359426651, "results": "16", "hashOfConfig": "10"}, {"size": 6874, "mtime": 1753358999666, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2qkwz", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\components\\OSIVisualization.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\components\\OSILayer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\components\\DetailPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\components\\DataFlowAnimation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test\\osi-model-interactive\\src\\types\\OSIModel.ts", [], []]