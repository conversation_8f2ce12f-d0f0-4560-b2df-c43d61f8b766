{"ast": null, "code": "const stepsOrder = [\"setup\",\n// Compute\n\"read\",\n// Read\n\"resolveKeyframes\",\n// Write/Read/Write/Read\n\"preUpdate\",\n// Compute\n\"update\",\n// Compute\n\"preRender\",\n// Compute\n\"render\",\n// Write\n\"postRender\" // Compute\n];\nexport { stepsOrder };", "map": {"version": 3, "names": ["stepsOrder"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/motion-dom/dist/es/frameloop/order.mjs"], "sourcesContent": ["const stepsOrder = [\n    \"setup\", // Compute\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"preUpdate\", // Compute\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\nexport { stepsOrder };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG,CACf,OAAO;AAAE;AACT,MAAM;AAAE;AACR,kBAAkB;AAAE;AACpB,WAAW;AAAE;AACb,QAAQ;AAAE;AACV,WAAW;AAAE;AACb,QAAQ;AAAE;AACV,YAAY,CAAE;AAAA,CACjB;AAED,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}