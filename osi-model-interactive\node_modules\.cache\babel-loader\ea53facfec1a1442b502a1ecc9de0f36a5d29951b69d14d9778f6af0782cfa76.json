{"ast": null, "code": "\"use client\";\n\nimport { jsx } from 'react/jsx-runtime';\nimport * as React from 'react';\nimport { useId, useMemo } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { PopChild } from './PopChild.mjs';\nconst PresenceChild = ({\n  children,\n  initial,\n  isPresent,\n  onExitComplete,\n  custom,\n  presenceAffectsLayout,\n  mode,\n  anchorX,\n  root\n}) => {\n  const presenceChildren = useConstant(newChildrenMap);\n  const id = useId();\n  let isReusedContext = true;\n  let context = useMemo(() => {\n    isReusedContext = false;\n    return {\n      id,\n      initial,\n      isPresent,\n      custom,\n      onExitComplete: childId => {\n        presenceChildren.set(childId, true);\n        for (const isComplete of presenceChildren.values()) {\n          if (!isComplete) return; // can stop searching when any is incomplete\n        }\n        onExitComplete && onExitComplete();\n      },\n      register: childId => {\n        presenceChildren.set(childId, false);\n        return () => presenceChildren.delete(childId);\n      }\n    };\n  }, [isPresent, presenceChildren, onExitComplete]);\n  /**\n   * If the presence of a child affects the layout of the components around it,\n   * we want to make a new context value to ensure they get re-rendered\n   * so they can detect that layout change.\n   */\n  if (presenceAffectsLayout && isReusedContext) {\n    context = {\n      ...context\n    };\n  }\n  useMemo(() => {\n    presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n  }, [isPresent]);\n  /**\n   * If there's no `motion` components to fire exit animations, we want to remove this\n   * component immediately.\n   */\n  React.useEffect(() => {\n    !isPresent && !presenceChildren.size && onExitComplete && onExitComplete();\n  }, [isPresent]);\n  if (mode === \"popLayout\") {\n    children = jsx(PopChild, {\n      isPresent: isPresent,\n      anchorX: anchorX,\n      root: root,\n      children: children\n    });\n  }\n  return jsx(PresenceContext.Provider, {\n    value: context,\n    children: children\n  });\n};\nfunction newChildrenMap() {\n  return new Map();\n}\nexport { PresenceChild };", "map": {"version": 3, "names": ["jsx", "React", "useId", "useMemo", "PresenceContext", "useConstant", "PopChild", "Presence<PERSON><PERSON><PERSON>", "children", "initial", "isPresent", "onExitComplete", "custom", "presenceAffectsLayout", "mode", "anchorX", "root", "presenceC<PERSON><PERSON>n", "newChildrenMap", "id", "isReusedContext", "context", "childId", "set", "isComplete", "values", "register", "delete", "for<PERSON>ach", "_", "key", "useEffect", "size", "Provider", "value", "Map"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport * as React from 'react';\nimport { useId, useMemo } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { PopChild } from './PopChild.mjs';\n\nconst PresenceChild = ({ children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, anchorX, root }) => {\n    const presenceChildren = useConstant(newChildrenMap);\n    const id = useId();\n    let isReusedContext = true;\n    let context = useMemo(() => {\n        isReusedContext = false;\n        return {\n            id,\n            initial,\n            isPresent,\n            custom,\n            onExitComplete: (childId) => {\n                presenceChildren.set(childId, true);\n                for (const isComplete of presenceChildren.values()) {\n                    if (!isComplete)\n                        return; // can stop searching when any is incomplete\n                }\n                onExitComplete && onExitComplete();\n            },\n            register: (childId) => {\n                presenceChildren.set(childId, false);\n                return () => presenceChildren.delete(childId);\n            },\n        };\n    }, [isPresent, presenceChildren, onExitComplete]);\n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */\n    if (presenceAffectsLayout && isReusedContext) {\n        context = { ...context };\n    }\n    useMemo(() => {\n        presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n    }, [isPresent]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */\n    React.useEffect(() => {\n        !isPresent &&\n            !presenceChildren.size &&\n            onExitComplete &&\n            onExitComplete();\n    }, [isPresent]);\n    if (mode === \"popLayout\") {\n        children = (jsx(PopChild, { isPresent: isPresent, anchorX: anchorX, root: root, children: children }));\n    }\n    return (jsx(PresenceContext.Provider, { value: context, children: children }));\n};\nfunction newChildrenMap() {\n    return new Map();\n}\n\nexport { PresenceChild };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,GAAG,QAAQ,mBAAmB;AACvC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,OAAO,QAAQ,OAAO;AACtC,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,QAAQ,QAAQ,gBAAgB;AAEzC,MAAMC,aAAa,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,SAAS;EAAEC,cAAc;EAAEC,MAAM;EAAEC,qBAAqB;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAK,CAAC,KAAK;EAC5H,MAAMC,gBAAgB,GAAGZ,WAAW,CAACa,cAAc,CAAC;EACpD,MAAMC,EAAE,GAAGjB,KAAK,CAAC,CAAC;EAClB,IAAIkB,eAAe,GAAG,IAAI;EAC1B,IAAIC,OAAO,GAAGlB,OAAO,CAAC,MAAM;IACxBiB,eAAe,GAAG,KAAK;IACvB,OAAO;MACHD,EAAE;MACFV,OAAO;MACPC,SAAS;MACTE,MAAM;MACND,cAAc,EAAGW,OAAO,IAAK;QACzBL,gBAAgB,CAACM,GAAG,CAACD,OAAO,EAAE,IAAI,CAAC;QACnC,KAAK,MAAME,UAAU,IAAIP,gBAAgB,CAACQ,MAAM,CAAC,CAAC,EAAE;UAChD,IAAI,CAACD,UAAU,EACX,OAAO,CAAC;QAChB;QACAb,cAAc,IAAIA,cAAc,CAAC,CAAC;MACtC,CAAC;MACDe,QAAQ,EAAGJ,OAAO,IAAK;QACnBL,gBAAgB,CAACM,GAAG,CAACD,OAAO,EAAE,KAAK,CAAC;QACpC,OAAO,MAAML,gBAAgB,CAACU,MAAM,CAACL,OAAO,CAAC;MACjD;IACJ,CAAC;EACL,CAAC,EAAE,CAACZ,SAAS,EAAEO,gBAAgB,EAAEN,cAAc,CAAC,CAAC;EACjD;AACJ;AACA;AACA;AACA;EACI,IAAIE,qBAAqB,IAAIO,eAAe,EAAE;IAC1CC,OAAO,GAAG;MAAE,GAAGA;IAAQ,CAAC;EAC5B;EACAlB,OAAO,CAAC,MAAM;IACVc,gBAAgB,CAACW,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKb,gBAAgB,CAACM,GAAG,CAACO,GAAG,EAAE,KAAK,CAAC,CAAC;EAC1E,CAAC,EAAE,CAACpB,SAAS,CAAC,CAAC;EACf;AACJ;AACA;AACA;EACIT,KAAK,CAAC8B,SAAS,CAAC,MAAM;IAClB,CAACrB,SAAS,IACN,CAACO,gBAAgB,CAACe,IAAI,IACtBrB,cAAc,IACdA,cAAc,CAAC,CAAC;EACxB,CAAC,EAAE,CAACD,SAAS,CAAC,CAAC;EACf,IAAII,IAAI,KAAK,WAAW,EAAE;IACtBN,QAAQ,GAAIR,GAAG,CAACM,QAAQ,EAAE;MAAEI,SAAS,EAAEA,SAAS;MAAEK,OAAO,EAAEA,OAAO;MAAEC,IAAI,EAAEA,IAAI;MAAER,QAAQ,EAAEA;IAAS,CAAC,CAAE;EAC1G;EACA,OAAQR,GAAG,CAACI,eAAe,CAAC6B,QAAQ,EAAE;IAAEC,KAAK,EAAEb,OAAO;IAAEb,QAAQ,EAAEA;EAAS,CAAC,CAAC;AACjF,CAAC;AACD,SAASU,cAAcA,CAAA,EAAG;EACtB,OAAO,IAAIiB,GAAG,CAAC,CAAC;AACpB;AAEA,SAAS5B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}