{"ast": null, "code": "import { motionValue } from 'motion-dom';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n  if (visualElement.hasValue(key)) {\n    visualElement.getValue(key).set(value);\n  } else {\n    visualElement.addValue(key, motionValue(value));\n  }\n}\nfunction resolveFinalValueInKeyframes(v) {\n  // TODO maybe throw if v.length - 1 is placeholder token?\n  return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n}\nfunction setTarget(visualElement, definition) {\n  const resolved = resolveVariant(visualElement, definition);\n  let {\n    transitionEnd = {},\n    transition = {},\n    ...target\n  } = resolved || {};\n  target = {\n    ...target,\n    ...transitionEnd\n  };\n  for (const key in target) {\n    const value = resolveFinalValueInKeyframes(target[key]);\n    setMotionValue(visualElement, key, value);\n  }\n}\nexport { setTarget };", "map": {"version": 3, "names": ["motionValue", "isKeyframesTarget", "resolveV<PERSON>t", "setMotionValue", "visualElement", "key", "value", "hasValue", "getValue", "set", "addValue", "resolveFinalValueInKeyframes", "v", "length", "<PERSON><PERSON><PERSON><PERSON>", "definition", "resolved", "transitionEnd", "transition", "target"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/render/utils/setters.mjs"], "sourcesContent": ["import { motionValue } from 'motion-dom';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, motionValue(value));\n    }\n}\nfunction resolveFinalValueInKeyframes(v) {\n    // TODO maybe throw if v.length - 1 is placeholder token?\n    return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = resolveVariant(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved || {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = resolveFinalValueInKeyframes(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\n\nexport { setTarget };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,YAAY;AACxC,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,cAAc,QAAQ,gCAAgC;;AAE/D;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,aAAa,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC/C,IAAIF,aAAa,CAACG,QAAQ,CAACF,GAAG,CAAC,EAAE;IAC7BD,aAAa,CAACI,QAAQ,CAACH,GAAG,CAAC,CAACI,GAAG,CAACH,KAAK,CAAC;EAC1C,CAAC,MACI;IACDF,aAAa,CAACM,QAAQ,CAACL,GAAG,EAAEL,WAAW,CAACM,KAAK,CAAC,CAAC;EACnD;AACJ;AACA,SAASK,4BAA4BA,CAACC,CAAC,EAAE;EACrC;EACA,OAAOX,iBAAiB,CAACW,CAAC,CAAC,GAAGA,CAAC,CAACA,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGD,CAAC;AAC1D;AACA,SAASE,SAASA,CAACV,aAAa,EAAEW,UAAU,EAAE;EAC1C,MAAMC,QAAQ,GAAGd,cAAc,CAACE,aAAa,EAAEW,UAAU,CAAC;EAC1D,IAAI;IAAEE,aAAa,GAAG,CAAC,CAAC;IAAEC,UAAU,GAAG,CAAC,CAAC;IAAE,GAAGC;EAAO,CAAC,GAAGH,QAAQ,IAAI,CAAC,CAAC;EACvEG,MAAM,GAAG;IAAE,GAAGA,MAAM;IAAE,GAAGF;EAAc,CAAC;EACxC,KAAK,MAAMZ,GAAG,IAAIc,MAAM,EAAE;IACtB,MAAMb,KAAK,GAAGK,4BAA4B,CAACQ,MAAM,CAACd,GAAG,CAAC,CAAC;IACvDF,cAAc,CAACC,aAAa,EAAEC,GAAG,EAAEC,KAAK,CAAC;EAC7C;AACJ;AAEA,SAASQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}