{"ast": null, "code": "\"use client\";\n\nimport { jsxs, jsx } from 'react/jsx-runtime';\nimport { warning, invariant } from 'motion-utils';\nimport { forwardRef, useContext } from 'react';\nimport { LayoutGroupContext } from '../context/LayoutGroupContext.mjs';\nimport { LazyContext } from '../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../context/MotionContext/index.mjs';\nimport { useCreateMotionContext } from '../context/MotionContext/create.mjs';\nimport { useRender } from '../render/dom/use-render.mjs';\nimport { isSVGComponent } from '../render/dom/utils/is-svg-component.mjs';\nimport { useHTMLVisualState } from '../render/html/use-html-visual-state.mjs';\nimport { useSVGVisualState } from '../render/svg/use-svg-visual-state.mjs';\nimport { isBrowser } from '../utils/is-browser.mjs';\nimport { featureDefinitions } from './features/definitions.mjs';\nimport { loadFeatures } from './features/load-features.mjs';\nimport { motionComponentSymbol } from './utils/symbol.mjs';\nimport { useMotionRef } from './utils/use-motion-ref.mjs';\nimport { useVisualElement } from './utils/use-visual-element.mjs';\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createMotionComponent(Component, {\n  forwardMotionProps = false\n} = {}, preloadedFeatures, createVisualElement) {\n  preloadedFeatures && loadFeatures(preloadedFeatures);\n  const useVisualState = isSVGComponent(Component) ? useSVGVisualState : useHTMLVisualState;\n  function MotionDOMComponent(props, externalRef) {\n    /**\n     * If we need to measure the element we load this functionality in a\n     * separate class component in order to gain access to getSnapshotBeforeUpdate.\n     */\n    let MeasureLayout;\n    const configAndProps = {\n      ...useContext(MotionConfigContext),\n      ...props,\n      layoutId: useLayoutId(props)\n    };\n    const {\n      isStatic\n    } = configAndProps;\n    const context = useCreateMotionContext(props);\n    const visualState = useVisualState(props, isStatic);\n    if (!isStatic && isBrowser) {\n      useStrictMode(configAndProps, preloadedFeatures);\n      const layoutProjection = getProjectionFunctionality(configAndProps);\n      MeasureLayout = layoutProjection.MeasureLayout;\n      /**\n       * Create a VisualElement for this component. A VisualElement provides a common\n       * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n       * providing a way of rendering to these APIs outside of the React render loop\n       * for more performant animations and interactions\n       */\n      context.visualElement = useVisualElement(Component, visualState, configAndProps, createVisualElement, layoutProjection.ProjectionNode);\n    }\n    /**\n     * The mount order and hierarchy is specific to ensure our element ref\n     * is hydrated by the time features fire their effects.\n     */\n    return jsxs(MotionContext.Provider, {\n      value: context,\n      children: [MeasureLayout && context.visualElement ? jsx(MeasureLayout, {\n        visualElement: context.visualElement,\n        ...configAndProps\n      }) : null, useRender(Component, props, useMotionRef(visualState, context.visualElement, externalRef), visualState, isStatic, forwardMotionProps)]\n    });\n  }\n  MotionDOMComponent.displayName = `motion.${typeof Component === \"string\" ? Component : `create(${Component.displayName ?? Component.name ?? \"\"})`}`;\n  const ForwardRefMotionComponent = forwardRef(MotionDOMComponent);\n  ForwardRefMotionComponent[motionComponentSymbol] = Component;\n  return ForwardRefMotionComponent;\n}\nfunction useLayoutId({\n  layoutId\n}) {\n  const layoutGroupId = useContext(LayoutGroupContext).id;\n  return layoutGroupId && layoutId !== undefined ? layoutGroupId + \"-\" + layoutId : layoutId;\n}\nfunction useStrictMode(configAndProps, preloadedFeatures) {\n  const isStrict = useContext(LazyContext).strict;\n  /**\n   * If we're in development mode, check to make sure we're not rendering a motion component\n   * as a child of LazyMotion, as this will break the file-size benefits of using it.\n   */\n  if (process.env.NODE_ENV !== \"production\" && preloadedFeatures && isStrict) {\n    const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n    configAndProps.ignoreStrict ? warning(false, strictMessage, \"lazy-strict-mode\") : invariant(false, strictMessage, \"lazy-strict-mode\");\n  }\n}\nfunction getProjectionFunctionality(props) {\n  const {\n    drag,\n    layout\n  } = featureDefinitions;\n  if (!drag && !layout) return {};\n  const combined = {\n    ...drag,\n    ...layout\n  };\n  return {\n    MeasureLayout: drag?.isEnabled(props) || layout?.isEnabled(props) ? combined.MeasureLayout : undefined,\n    ProjectionNode: combined.ProjectionNode\n  };\n}\nexport { createMotionComponent };", "map": {"version": 3, "names": ["jsxs", "jsx", "warning", "invariant", "forwardRef", "useContext", "LayoutGroupContext", "LazyContext", "MotionConfigContext", "MotionContext", "useCreateMotionContext", "useRender", "isSVGComponent", "useHTMLVisualState", "useSVGVisualState", "<PERSON><PERSON><PERSON><PERSON>", "featureDefinitions", "loadFeatures", "motionComponentSymbol", "useMotionRef", "useVisualElement", "createMotionComponent", "Component", "forwardMotionProps", "preloadedFeatures", "createVisualElement", "useVisualState", "MotionDOMComponent", "props", "externalRef", "MeasureLayout", "configAndProps", "layoutId", "useLayoutId", "isStatic", "context", "visualState", "useStrictMode", "layoutProjection", "getProjectionFunctionality", "visualElement", "ProjectionNode", "Provider", "value", "children", "displayName", "name", "ForwardRefMotionComponent", "layoutGroupId", "id", "undefined", "isStrict", "strict", "process", "env", "NODE_ENV", "strictMessage", "ignoreStrict", "drag", "layout", "combined", "isEnabled"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/motion/index.mjs"], "sourcesContent": ["\"use client\";\nimport { jsxs, jsx } from 'react/jsx-runtime';\nimport { warning, invariant } from 'motion-utils';\nimport { forwardRef, useContext } from 'react';\nimport { LayoutGroupContext } from '../context/LayoutGroupContext.mjs';\nimport { LazyContext } from '../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../context/MotionContext/index.mjs';\nimport { useCreateMotionContext } from '../context/MotionContext/create.mjs';\nimport { useRender } from '../render/dom/use-render.mjs';\nimport { isSVGComponent } from '../render/dom/utils/is-svg-component.mjs';\nimport { useHTMLVisualState } from '../render/html/use-html-visual-state.mjs';\nimport { useSVGVisualState } from '../render/svg/use-svg-visual-state.mjs';\nimport { isBrowser } from '../utils/is-browser.mjs';\nimport { featureDefinitions } from './features/definitions.mjs';\nimport { loadFeatures } from './features/load-features.mjs';\nimport { motionComponentSymbol } from './utils/symbol.mjs';\nimport { useMotionRef } from './utils/use-motion-ref.mjs';\nimport { useVisualElement } from './utils/use-visual-element.mjs';\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createMotionComponent(Component, { forwardMotionProps = false } = {}, preloadedFeatures, createVisualElement) {\n    preloadedFeatures && loadFeatures(preloadedFeatures);\n    const useVisualState = isSVGComponent(Component)\n        ? useSVGVisualState\n        : useHTMLVisualState;\n    function MotionDOMComponent(props, externalRef) {\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */\n        let MeasureLayout;\n        const configAndProps = {\n            ...useContext(MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props),\n        };\n        const { isStatic } = configAndProps;\n        const context = useCreateMotionContext(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && isBrowser) {\n            useStrictMode(configAndProps, preloadedFeatures);\n            const layoutProjection = getProjectionFunctionality(configAndProps);\n            MeasureLayout = layoutProjection.MeasureLayout;\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */\n            context.visualElement = useVisualElement(Component, visualState, configAndProps, createVisualElement, layoutProjection.ProjectionNode);\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */\n        return (jsxs(MotionContext.Provider, { value: context, children: [MeasureLayout && context.visualElement ? (jsx(MeasureLayout, { visualElement: context.visualElement, ...configAndProps })) : null, useRender(Component, props, useMotionRef(visualState, context.visualElement, externalRef), visualState, isStatic, forwardMotionProps)] }));\n    }\n    MotionDOMComponent.displayName = `motion.${typeof Component === \"string\"\n        ? Component\n        : `create(${Component.displayName ?? Component.name ?? \"\"})`}`;\n    const ForwardRefMotionComponent = forwardRef(MotionDOMComponent);\n    ForwardRefMotionComponent[motionComponentSymbol] = Component;\n    return ForwardRefMotionComponent;\n}\nfunction useLayoutId({ layoutId }) {\n    const layoutGroupId = useContext(LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined\n        ? layoutGroupId + \"-\" + layoutId\n        : layoutId;\n}\nfunction useStrictMode(configAndProps, preloadedFeatures) {\n    const isStrict = useContext(LazyContext).strict;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */\n    if (process.env.NODE_ENV !== \"production\" &&\n        preloadedFeatures &&\n        isStrict) {\n        const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n        configAndProps.ignoreStrict\n            ? warning(false, strictMessage, \"lazy-strict-mode\")\n            : invariant(false, strictMessage, \"lazy-strict-mode\");\n    }\n}\nfunction getProjectionFunctionality(props) {\n    const { drag, layout } = featureDefinitions;\n    if (!drag && !layout)\n        return {};\n    const combined = { ...drag, ...layout };\n    return {\n        MeasureLayout: drag?.isEnabled(props) || layout?.isEnabled(props)\n            ? combined.MeasureLayout\n            : undefined,\n        ProjectionNode: combined.ProjectionNode,\n    };\n}\n\nexport { createMotionComponent };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,IAAI,EAAEC,GAAG,QAAQ,mBAAmB;AAC7C,SAASC,OAAO,EAAEC,SAAS,QAAQ,cAAc;AACjD,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,cAAc,QAAQ,0CAA0C;AACzE,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,gBAAgB,QAAQ,gCAAgC;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EAAEC,kBAAkB,GAAG;AAAM,CAAC,GAAG,CAAC,CAAC,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAE;EACnHD,iBAAiB,IAAIP,YAAY,CAACO,iBAAiB,CAAC;EACpD,MAAME,cAAc,GAAGd,cAAc,CAACU,SAAS,CAAC,GAC1CR,iBAAiB,GACjBD,kBAAkB;EACxB,SAASc,kBAAkBA,CAACC,KAAK,EAAEC,WAAW,EAAE;IAC5C;AACR;AACA;AACA;IACQ,IAAIC,aAAa;IACjB,MAAMC,cAAc,GAAG;MACnB,GAAG1B,UAAU,CAACG,mBAAmB,CAAC;MAClC,GAAGoB,KAAK;MACRI,QAAQ,EAAEC,WAAW,CAACL,KAAK;IAC/B,CAAC;IACD,MAAM;MAAEM;IAAS,CAAC,GAAGH,cAAc;IACnC,MAAMI,OAAO,GAAGzB,sBAAsB,CAACkB,KAAK,CAAC;IAC7C,MAAMQ,WAAW,GAAGV,cAAc,CAACE,KAAK,EAAEM,QAAQ,CAAC;IACnD,IAAI,CAACA,QAAQ,IAAInB,SAAS,EAAE;MACxBsB,aAAa,CAACN,cAAc,EAAEP,iBAAiB,CAAC;MAChD,MAAMc,gBAAgB,GAAGC,0BAA0B,CAACR,cAAc,CAAC;MACnED,aAAa,GAAGQ,gBAAgB,CAACR,aAAa;MAC9C;AACZ;AACA;AACA;AACA;AACA;MACYK,OAAO,CAACK,aAAa,GAAGpB,gBAAgB,CAACE,SAAS,EAAEc,WAAW,EAAEL,cAAc,EAAEN,mBAAmB,EAAEa,gBAAgB,CAACG,cAAc,CAAC;IAC1I;IACA;AACR;AACA;AACA;IACQ,OAAQzC,IAAI,CAACS,aAAa,CAACiC,QAAQ,EAAE;MAAEC,KAAK,EAAER,OAAO;MAAES,QAAQ,EAAE,CAACd,aAAa,IAAIK,OAAO,CAACK,aAAa,GAAIvC,GAAG,CAAC6B,aAAa,EAAE;QAAEU,aAAa,EAAEL,OAAO,CAACK,aAAa;QAAE,GAAGT;MAAe,CAAC,CAAC,GAAI,IAAI,EAAEpB,SAAS,CAACW,SAAS,EAAEM,KAAK,EAAET,YAAY,CAACiB,WAAW,EAAED,OAAO,CAACK,aAAa,EAAEX,WAAW,CAAC,EAAEO,WAAW,EAAEF,QAAQ,EAAEX,kBAAkB,CAAC;IAAE,CAAC,CAAC;EAClV;EACAI,kBAAkB,CAACkB,WAAW,GAAG,UAAU,OAAOvB,SAAS,KAAK,QAAQ,GAClEA,SAAS,GACT,UAAUA,SAAS,CAACuB,WAAW,IAAIvB,SAAS,CAACwB,IAAI,IAAI,EAAE,GAAG,EAAE;EAClE,MAAMC,yBAAyB,GAAG3C,UAAU,CAACuB,kBAAkB,CAAC;EAChEoB,yBAAyB,CAAC7B,qBAAqB,CAAC,GAAGI,SAAS;EAC5D,OAAOyB,yBAAyB;AACpC;AACA,SAASd,WAAWA,CAAC;EAAED;AAAS,CAAC,EAAE;EAC/B,MAAMgB,aAAa,GAAG3C,UAAU,CAACC,kBAAkB,CAAC,CAAC2C,EAAE;EACvD,OAAOD,aAAa,IAAIhB,QAAQ,KAAKkB,SAAS,GACxCF,aAAa,GAAG,GAAG,GAAGhB,QAAQ,GAC9BA,QAAQ;AAClB;AACA,SAASK,aAAaA,CAACN,cAAc,EAAEP,iBAAiB,EAAE;EACtD,MAAM2B,QAAQ,GAAG9C,UAAU,CAACE,WAAW,CAAC,CAAC6C,MAAM;EAC/C;AACJ;AACA;AACA;EACI,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrC/B,iBAAiB,IACjB2B,QAAQ,EAAE;IACV,MAAMK,aAAa,GAAG,kJAAkJ;IACxKzB,cAAc,CAAC0B,YAAY,GACrBvD,OAAO,CAAC,KAAK,EAAEsD,aAAa,EAAE,kBAAkB,CAAC,GACjDrD,SAAS,CAAC,KAAK,EAAEqD,aAAa,EAAE,kBAAkB,CAAC;EAC7D;AACJ;AACA,SAASjB,0BAA0BA,CAACX,KAAK,EAAE;EACvC,MAAM;IAAE8B,IAAI;IAAEC;EAAO,CAAC,GAAG3C,kBAAkB;EAC3C,IAAI,CAAC0C,IAAI,IAAI,CAACC,MAAM,EAChB,OAAO,CAAC,CAAC;EACb,MAAMC,QAAQ,GAAG;IAAE,GAAGF,IAAI;IAAE,GAAGC;EAAO,CAAC;EACvC,OAAO;IACH7B,aAAa,EAAE4B,IAAI,EAAEG,SAAS,CAACjC,KAAK,CAAC,IAAI+B,MAAM,EAAEE,SAAS,CAACjC,KAAK,CAAC,GAC3DgC,QAAQ,CAAC9B,aAAa,GACtBoB,SAAS;IACfT,cAAc,EAAEmB,QAAQ,CAACnB;EAC7B,CAAC;AACL;AAEA,SAASpB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}