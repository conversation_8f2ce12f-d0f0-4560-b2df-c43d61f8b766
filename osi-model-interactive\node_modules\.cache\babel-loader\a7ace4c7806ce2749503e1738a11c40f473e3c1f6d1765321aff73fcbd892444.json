{"ast": null, "code": "import { HoverGesture } from '../../gestures/hover.mjs';\nimport { FocusGesture } from '../../gestures/focus.mjs';\nimport { PressGesture } from '../../gestures/press.mjs';\nimport { InViewFeature } from './viewport/index.mjs';\nconst gestureAnimations = {\n  inView: {\n    Feature: InViewFeature\n  },\n  tap: {\n    Feature: PressGesture\n  },\n  focus: {\n    Feature: FocusGesture\n  },\n  hover: {\n    Feature: HoverGesture\n  }\n};\nexport { gestureAnimations };", "map": {"version": 3, "names": ["HoverGesture", "FocusGesture", "PressGesture", "InViewFeature", "gestureAnimations", "inView", "Feature", "tap", "focus", "hover"], "sources": ["C:/Users/<USER>/Desktop/test/osi-model-interactive/node_modules/framer-motion/dist/es/motion/features/gestures.mjs"], "sourcesContent": ["import { HoverGesture } from '../../gestures/hover.mjs';\nimport { FocusGesture } from '../../gestures/focus.mjs';\nimport { PressGesture } from '../../gestures/press.mjs';\nimport { InViewFeature } from './viewport/index.mjs';\n\nconst gestureAnimations = {\n    inView: {\n        Feature: InViewFeature,\n    },\n    tap: {\n        Feature: PressGesture,\n    },\n    focus: {\n        Feature: FocusGesture,\n    },\n    hover: {\n        Feature: HoverGesture,\n    },\n};\n\nexport { gestureAnimations };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,0BAA0B;AACvD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,MAAMC,iBAAiB,GAAG;EACtBC,MAAM,EAAE;IACJC,OAAO,EAAEH;EACb,CAAC;EACDI,GAAG,EAAE;IACDD,OAAO,EAAEJ;EACb,CAAC;EACDM,KAAK,EAAE;IACHF,OAAO,EAAEL;EACb,CAAC;EACDQ,KAAK,EAAE;IACHH,OAAO,EAAEN;EACb;AACJ,CAAC;AAED,SAASI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}