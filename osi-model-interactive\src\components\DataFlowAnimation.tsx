import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { OSI_LAYERS } from '../types/OSIModel';

interface DataFlowAnimationProps {
  direction: 'down' | 'up';
  onComplete: () => void;
}

interface DataPacket {
  id: string;
  content: string;
  currentLayer: number;
  headers: string[];
}

const DataFlowAnimation: React.FC<DataFlowAnimationProps> = ({ direction, onComplete }) => {
  const [currentPacket, setCurrentPacket] = useState<DataPacket | null>(null);
  const [animationStep, setAnimationStep] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  const initialData = direction === 'down' 
    ? { content: "Hello World!", headers: [] }
    : { content: "HTTP Response", headers: ["TCP Header", "IP Header", "Ethernet Header"] };

  useEffect(() => {
    if (isComplete) return;

    const layers = direction === 'down' ? OSI_LAYERS : [...OSI_LAYERS].reverse();
    const totalSteps = layers.length;

    if (animationStep < totalSteps) {
      const currentLayer = layers[animationStep];
      const timer = setTimeout(() => {
        setCurrentPacket({
          id: `packet-${animationStep}`,
          content: initialData.content,
          currentLayer: currentLayer.id,
          headers: direction === 'down'
            ? [...initialData.headers, `${currentLayer.name} Header`]
            : initialData.headers.slice(0, -animationStep)
        });
        setAnimationStep(prev => prev + 1);
      }, 800);

      return () => clearTimeout(timer);
    } else {
      const timer = setTimeout(() => {
        setIsComplete(true);
        onComplete();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [animationStep, direction, onComplete, isComplete, initialData.content, initialData.headers]);

  const getPacketPosition = (layerId: number) => {
    const layer = OSI_LAYERS.find(l => l.id === layerId);
    return layer ? layer.position.y + 40 : 0;
  };

  const getHeadersDisplay = () => {
    if (!currentPacket) return [];
    
    if (direction === 'down') {
      // 发送时，逐层添加头部
      return currentPacket.headers;
    } else {
      // 接收时，逐层移除头部
      return currentPacket.headers;
    }
  };

  return (
    <div className="data-flow-animation">
      <AnimatePresence>
        {currentPacket && !isComplete && (
          <motion.div
            className="data-packet"
            initial={{ 
              x: direction === 'down' ? -100 : 600,
              y: getPacketPosition(currentPacket.currentLayer),
              scale: 0.8,
              opacity: 0
            }}
            animate={{ 
              x: 500,
              y: getPacketPosition(currentPacket.currentLayer),
              scale: 1,
              opacity: 1
            }}
            exit={{ 
              x: direction === 'down' ? 600 : -100,
              opacity: 0,
              scale: 0.8
            }}
            transition={{ 
              duration: 0.6,
              ease: "easeInOut"
            }}
          >
            <div className="packet-content">
              <div className="packet-data">
                {currentPacket.content}
              </div>
              
              <div className="packet-headers">
                {getHeadersDisplay().map((header, index) => (
                  <motion.div
                    key={index}
                    className="packet-header"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    {header}
                  </motion.div>
                ))}
              </div>
            </div>

            <div className="packet-arrow">
              {direction === 'down' ? '⬇️' : '⬆️'}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div className="flow-info">
        <h3>
          {direction === 'down' ? '📤 数据发送过程' : '📥 数据接收过程'}
        </h3>
        <p>
          {direction === 'down' 
            ? '数据从应用层向下传递，每层添加自己的头部信息'
            : '数据从物理层向上传递，每层移除对应的头部信息'
          }
        </p>
        
        {currentPacket && (
          <div className="current-layer-info">
            <strong>当前处理层:</strong> 
            {OSI_LAYERS.find(l => l.id === currentPacket.currentLayer)?.name}
          </div>
        )}
      </div>

      <div className="process-steps">
        {OSI_LAYERS.map((layer, index) => {
          const stepIndex = direction === 'down' ? 7 - layer.id : layer.id - 1;
          const isActive = animationStep > stepIndex;
          const isCurrent = animationStep === stepIndex + 1;
          
          return (
            <motion.div
              key={layer.id}
              className={`process-step ${isActive ? 'completed' : ''} ${isCurrent ? 'current' : ''}`}
              initial={{ opacity: 0.3 }}
              animate={{ 
                opacity: isActive ? 1 : isCurrent ? 0.8 : 0.3,
                scale: isCurrent ? 1.1 : 1
              }}
              transition={{ duration: 0.3 }}
            >
              <div className="step-number">{layer.id}</div>
              <div className="step-name">{layer.name}</div>
              {isActive && (
                <motion.div
                  className="step-checkmark"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  ✓
                </motion.div>
              )}
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default DataFlowAnimation;
